#!/usr/bin/env python3
"""
Simple test script for core functionality
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_basic_functionality():
    """Test basic functionality with minimal dependencies"""
    print("🧪 Testing Enhanced Trading Application - Core Features")
    print("=" * 60)
    
    # Test 1: Data Fetcher
    print("\n📊 Testing Enhanced Data Fetcher...")
    try:
        from enhanced_data_fetcher import EnhancedDataFetcher
        
        fetcher = EnhancedDataFetcher()
        sample_data = fetcher._generate_sample_data("BTCUSDT", 50)
        
        if sample_data is not None and len(sample_data) > 0:
            print(f"✅ Sample data generated: {len(sample_data)} records")
            print(f"   Date range: {sample_data['Timestamp'].min()} to {sample_data['Timestamp'].max()}")
            print(f"   Price range: ${sample_data['Close'].min():.2f} - ${sample_data['Close'].max():.2f}")
        else:
            print("❌ Failed to generate sample data")
            return False
            
    except Exception as e:
        print(f"❌ Data fetcher error: {e}")
        return False
    
    # Test 2: Enhanced Backtester
    print("\n💰 Testing Enhanced Backtester...")
    try:
        from enhanced_backtester import EnhancedBacktester, BacktestConfig
        
        # Create test data
        dates = pd.date_range('2023-01-01', periods=30, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(29):
            change = np.random.normal(0, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 30)
        }, index=dates)
        
        # Add simple signals
        df['signal'] = 0
        df.loc[df.index[::5], 'signal'] = 1   # Buy every 5 days
        df.loc[df.index[2::5], 'signal'] = -1  # Sell 2 days later
        
        config = BacktestConfig(initial_capital=10000, commission_rate=0.001)
        backtester = EnhancedBacktester(config)
        results = backtester.run_backtest(df)
        
        print(f"✅ Backtest completed:")
        print(f"   • Trades: {results['total_trades']}")
        print(f"   • Success Rate: {results['success_rate']}%")
        print(f"   • Total P&L: ${results['total_pnl']:.2f} ({results['total_pnl_percentage']:.2f}%)")
        print(f"   • Max Drawdown: {results['max_drawdown']:.2f}%")
        
    except Exception as e:
        print(f"❌ Backtester error: {e}")
        return False
    
    # Test 3: Enhanced Indicators (basic)
    print("\n🔧 Testing Enhanced Indicators...")
    try:
        from enhanced_indicators import EnhancedIndicators
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(49):
            change = np.random.normal(0, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 50)
        }, index=dates)
        
        # Test individual indicators
        bb = EnhancedIndicators.bollinger_bands(df['Close'])
        stoch = EnhancedIndicators.stochastic(df['High'], df['Low'], df['Close'])
        williams = EnhancedIndicators.williams_r(df['High'], df['Low'], df['Close'])
        
        print(f"✅ Individual indicators calculated:")
        print(f"   • Bollinger Bands: {len(bb)} components")
        print(f"   • Stochastic: {len(stoch)} components")
        print(f"   • Williams %R: {len(williams)} values")
        
        # Test all indicators
        enhanced_df = EnhancedIndicators.calculate_all_indicators(df)
        new_indicators = len(enhanced_df.columns) - len(df.columns)
        print(f"   • Total new indicators: {new_indicators}")
        
    except Exception as e:
        print(f"❌ Indicators error: {e}")
        return False
    
    # Test 4: Parameter Optimizer (basic)
    print("\n🎯 Testing Parameter Optimizer...")
    try:
        from parameter_optimizer import ParameterOptimizer, ParameterRange
        
        # Simple objective function
        def simple_objective(params):
            score = params.get('param1', 10) * 0.5 + np.random.random() * 2
            return {
                'total_pnl_percentage': score,
                'success_rate': np.random.uniform(40, 80),
                'total_trades': np.random.randint(5, 20)
            }
        
        param_ranges = [
            ParameterRange('param1', 5, 15, param_type='int')
        ]
        
        optimizer = ParameterOptimizer(simple_objective, 'total_pnl_percentage')
        results = optimizer.random_search(param_ranges, n_iterations=5)
        
        if results and len(results) > 0:
            best = results[0]
            print(f"✅ Optimization completed:")
            print(f"   • Best score: {best.score:.2f}")
            print(f"   • Best params: {best.parameters}")
        else:
            print("❌ No optimization results")
            return False
            
    except Exception as e:
        print(f"❌ Optimizer error: {e}")
        return False
    
    # Test 5: Configuration
    print("\n⚙️ Testing Configuration...")
    try:
        from config import AppConfig
        
        config = AppConfig()
        print(f"✅ Configuration loaded:")
        print(f"   • Default symbol: {config.data.default_symbol}")
        print(f"   • RSI length: {config.indicators.rsi_length}")
        print(f"   • Initial capital: ${config.backtest.initial_capital}")
        
        custom_params = config.get_custom_indicator_params()
        enhanced_params = config.get_enhanced_indicator_params()
        print(f"   • Custom params: {len(custom_params)} items")
        print(f"   • Enhanced params: {len(enhanced_params)} items")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Test 6: Integration Test
    print("\n🔗 Testing Integration...")
    try:
        from enhanced_main import TradingApplication
        
        app = TradingApplication()
        print(f"✅ TradingApplication initialized:")
        print(f"   • Data fetcher: {type(app.data_fetcher).__name__}")
        print(f"   • Indicators: {type(app.indicators).__name__}")
        
    except Exception as e:
        print(f"❌ Integration error: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ALL CORE TESTS PASSED!")
    print("=" * 60)
    print("\n🚀 Next Steps:")
    print("1. Install additional dependencies: pip install yfinance streamlit")
    print("2. Run the enhanced application: python enhanced_main.py")
    print("3. Launch the dashboard: python run_dashboard.py")
    print("4. Read the documentation in README.md")
    
    return True

if __name__ == '__main__':
    success = test_basic_functionality()
    sys.exit(0 if success else 1)
