o

    {$Qh�<  �                
   @   sv  d Z ddlZddlZddlmZmZ ddlZe�	d� G dd� d�Z
dejdejfd	d
�Ze
dk�r9ed� ejd
ddd�Zej�d� dgZed�D ]Zej�dd�Ze�ed de  � qKejedd� eD �dd� eD �eej�ddd�d�ed�Zej�ddgd�ed< ej�d�ed < ej�d�ed!< ed  ed!  ed"< ej�d#d$d�ed%< ed& d' ed(< ed& d) ed*< ed& ed+< ed& d, ed-< ej�ddgd�ed.< e
� Ze�e�Z e�!e�Z"e�#e�Z$ed/e dk�%� � �� ed0e"dk�%� � �� ed1e$dk�%� � �� ee�Z&ed2e&d3 dk�%� � �� ed4e&d3 dk�%� � �� ed5e&d3 dk�%� � �� ed6� dS dS )7zm
Improved Signal Generation System
Creates profitable trading signals based on multiple indicator confluence
�    N)�Dict�Tuple�ignorec                	   @   s�   e Zd ZdZdd� Zdejdejfdd�Zdejdejfdd	�Z	dejdejfd
d�Z
dejdejfdd
�Zddejdee
ef dejfdd�Zddejdejdedejfdd�Zdejdejdejfdd�ZdS )�ImprovedSignalGeneratorzL
    Advanced signal generation with multiple strategies and confluence
    c                 C   s
   g | _ d S �N)�signal_history)�self� r	   �J/mnt/persist/workspace/tradingview_backtester/improved_signal_generator.py�__init__   s   
z ImprovedSignalGenerator.__init__�df�returnc                 C   s�  t jd|jd�}d|jv }d|jv od|jv }d|jv }|s(|s(|s(td� |S t jd|jd�}t jd|jd�}|rd|d d	k}|d dk}	||d �d
�dk@ }
|	|d �d
�d	k@ }||
O }||O }|r�|d |d k|d �d
�|d �d
�k@ }|d |d k |d �d
�|d �d
�k@ }
||O }||
O }|r�|d dk |d |d �d
�k@ }|d dk|d |d �d
�k @ }||O }||O }d
|j|< d
|j|< |S )zO
        Generate trend-following signals based on multiple indicators
        r   ��index�pt_trend�	macd_line�macd_signal_line�rsiu:   ⚠️  No suitable indicators for trend following signalsFT�   �   �F   �����)�pd�Seriesr   �columns�print�shift�loc)r   r   �signals�has_pt_trend�has_macd�has_rsi�buy_conditions�sell_conditions�
pt_bullish�
pt_bearish�pt_buy�pt_sell�macd_bullish_cross�macd_bearish_cross�rsi_oversold_bounce�rsi_overbought_fallr	   r	   r
   � generate_trend_following_signals   sF   

����""

z8ImprovedSignalGenerator.generate_trend_following_signalsc                    s�   t jd� jd�}t� fdd�dD ��}d� jv }|s"|s"td� |S t jd� jd�}t jd� jd�}|rL� d	 � d
 k }� d	 � d k}||O }||O }|rb� d dk }	� d d
k}
||	O }||
O }d|j|< d|j|< |S )zO
        Generate mean reversion signals using Bollinger Bands and RSI
        r   r   c                 3   �   � | ]}|� j v V  qd S r   �r   ��.0�col�r   r	   r
   �	<genexpr>R   �   � zJImprovedSignalGenerator.generate_mean_reversion_signals.<locals>.<genexpr>)�bb_upper�bb_lower�	bb_middler   u9   ⚠️  No suitable indicators for mean reversion signalsF�Closer6   r5   �   �K   r   r   )r   r   r   �allr   r   r   )r   r   r   �has_bbr!   r"   r#   �price_below_lower_bb�price_above_upper_bb�rsi_oversold�rsi_overboughtr	   r2   r
   �generate_mean_reversion_signalsK   s*   


z7ImprovedSignalGenerator.generate_mean_reversion_signalsc                    s�  t jd� jd�}t� fdd�dD ��}d� jv }t� fdd�dD ��}|s-|s-td	� |S t jd
� jd�}t jd
� jd�}|r�� d � d k� d �d
�� d �d
�k@ }� d � d k � d �d
�� d �d
�k@ }	||O }||	O }|r�� d � d �d
�k}
� d � d �d
�k }||
M }||M }|rو d dk }� d dk}
� d � d k� d �d
�� d �d
�k@ }� d � d k � d �d
�� d �d
�k@ }|||@ O }||
|@ O }d
|j|< d|j|< |S )zK
        Generate momentum-based signals using MACD and Stochastic
        r   r   c                 3   r-   r   r.   r/   r2   r	   r
   r3   x   r4   zDImprovedSignalGenerator.generate_momentum_signals.<locals>.<genexpr>)r   r   �	macd_histc                 3   r-   r   r.   r/   r2   r	   r
   r3   z   r4   )�stoch_k�stoch_du3   ⚠️  No suitable indicators for momentum signalsFr   r   r   rC   �   �P   rD   r   )r   r   r   r;   r   r   r   r   )r   r   r   r    �
has_macd_hist�	has_stochr"   r#   r(   r)   �macd_hist_increasing�macd_hist_decreasing�stoch_oversold�stoch_overbought�stoch_bullish_cross�stoch_bearish_crossr	   r2   r
   �generate_momentum_signalsq   sR   
��������

z1ImprovedSignalGenerator.generate_momentum_signalsc                 C   s�   t jd|jd�}d|jvsd|jvrtd� |S |d dk}|d dk}|d dk|d �d�dk@ }|d dk|d �d�dk@ }d|jv ra|d �d	��� }|d |d
 k}||@ }	||@ }
n|}	|}
d|j|	< d|j|
< |S )zG
        Generate breakout signals using SuperTrend and volume
        r   r   �
supertrend�supertrend_directionu:   ⚠️  Missing SuperTrend indicators for breakout signalsr   r   �VolumerE   g333333�?)	r   r   r   r   r   r   �rolling�meanr   )r   r   r   �
st_bullish�
st_bearish�st_direction_change_bull�st_direction_change_bear�
volume_avg�high_volumer"   r#   r	   r	   r
   �generate_breakout_signals�   s0   
��
��



z1ImprovedSignalGenerator.generate_breakout_signalsN�strategy_weightsc           
      C   s�   |du rddddd�}t d� | �|�}| �|�}| �|�}| �|�}||d  ||d	   ||d
   ||d   }tjd|jd
�}d}	d}
d|j||	k< d|j||
k< | �	||�}|dk�
� }|dk�
� }t d|� d|� d�� |S )zH
        Generate signals based on multiple strategy confluence
        Ng�������?g�������?g333333�?g�������?)�trend_following�mean_reversion�momentum�breakoutu>   🎯 Generating confluence signals from multiple strategies...r]   r^   r_   r`   r   r   g333333ӿr   r   u   ✅ Generated z buy signals and z
 sell signals)r   r,   rA   rO   r[   r   r   r   r   �_filter_signals�sum)
r   r   r\   �
trend_signals�mean_reversion_signals�momentum_signals�breakout_signals�confluence_score�
final_signals�
buy_threshold�sell_threshold�buy_signals�sell_signalsr	   r	   r
   �generate_confluence_signals�   s:   �





�
�
��z3ImprovedSignalGenerator.generate_confluence_signals�   r   �min_bars_betweenc           	      C   s�   |� � }d}tt|��D ]}|j| dkr'|dur%|| |k r%d|j|< q|}qd|jv rC|d �d��� d }|d |k }d|j|< |S )zD
        Filter signals to reduce noise and improve quality
        Nr   �atrrE   �      �?)�copy�range�len�ilocr   rS   rT   r   )	r   r   r   ro   �filtered_signals�last_signal_idx�i�
atr_threshold�low_volatilityr	   r	   r
   ra     s   �

z'ImprovedSignalGenerator._filter_signalsc           	      C   s<  |� � }||d< g }d|jv r8t�|dkt�dd|d  d �t�|dkt�d|d d d �d��}|�|� d|jv r[t�|d �|d �d	���  }t�	|dd
�d
 }|�|� d|jv r{|d �d	��
� }t�	|d | dd�d }|�|� |r�tj
|dd
�}t�|dk|d�|d< |S t�|dkdd�|d< |S )zA
        Add signal strength indicators to the dataframe
        �signalr   r   r   r   r   r   rB   rE   �   rR   �   )�axis�signal_strengthrq   )rr   r   �np�where�maximum�append�absrS   �std�cliprT   )	r   r   r   �strength_factors�rsi_strength�
macd_strengthrY   �volume_strengthr   r	   r	   r
   �add_signal_strength#  s8   
��
	
 


�z+ImprovedSignalGenerator.add_signal_strengthr   )rn   )�__name__�
__module__�__qualname__�__doc__r   r   �	DataFramer   r,   rA   rO   r[   r   �str�floatrm   �intra   r�   r	   r	   r	   r
   r      s    7&@$)"1 r   r   r
   c              	   C   sT   t d� t� }|�| �}|�| |�}t�|d dkdt�|d dkdd��|d< |S )	z=
    Improve existing signal generation in the dataframe
    u#   🔧 Improving signal generation...r{   r   zrgba(0,180,0,0.7)r   zrgba(180,0,0,0.7)zrgba(128,128,128,0.5)�bar_plot_color)r   r   rm   r�   r�   r�   )r   �signal_generator�improved_signals�df_improvedr	   r	   r
   �improve_existing_signalsO  s   
��r�   �__main__z)=== Testing Improved Signal Generator ===z
2023-01-01�d   �D)�periods�freq�*   �c   g{�G�z�?r   r   c                 C   �   g | ]}|d  �qS )�R���Q�?r	   �r0   �pr	   r	   r
   �
<listcomp>w  �    r�   c                 C   r�   )�\���(\�?r	   r�   r	   r	   r
   r�   x  r�   i�  i�  )�Open�High�Lowr8   rR   r   TFr   r   r   rB   rE   rF   r   r8   r�   r5   r�   r6   r7   g�G�z��?rP   rQ   zTrend following signals: zMomentum signals: zConfluence signals: zFinal improved signals: r{   z
Buy signals: zSell signals: u#   ✅ Signal generator test completed)'r�   �pandasr   �numpyr�   �typingr   r   �warnings�filterwarningsr   r�   r�   r�   r   �
date_range�dates�random�seed�pricesrs   �_�normal�changer�   �randintr   �choice�randn�uniform�
signal_genr,   rc   rO   re   rm   �confluence_signalsrb   �improved_dfr	   r	   r	   r
   �<module>   sd    
  E
��	


�