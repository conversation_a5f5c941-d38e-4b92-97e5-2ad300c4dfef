from data_fetcher import fetch_binance_klines
from chart_plotter import plot_candlestick_chart_plotly
from custom_indicator import calculate_custom_indicator
from backtester import run_backtest # Import the backtester
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go
import os

def get_sample_data(days=60): # Ensure enough data for lookbacks
    print("Generating sample data as fallback...")
    # Generate more data initially then slice to 'days' to ensure variety and enough history for indicators
    generate_n_days = days + 150 # Generate more to allow for internal lookbacks before final slice

    base_date = datetime.now() - timedelta(days=generate_n_days)
    dates = [base_date + timedelta(days=i) for i in range(generate_n_days)]

    open_prices = np.full(generate_n_days, 16000.0)
    close_prices = np.full(generate_n_days, 16000.0)
    high_prices = np.full(generate_n_days, 16000.0)
    low_prices = np.full(generate_n_days, 16000.0)
    volume_data = np.random.randint(500, 2000, size=generate_n_days)

    for i in range(1, generate_n_days):
        change = np.random.randn() * 100
        open_prices[i] = close_prices[i-1]
        close_prices[i] = open_prices[i] + change
        high_prices[i] = max(open_prices[i], close_prices[i]) + np.random.rand() * 50
        low_prices[i] = min(open_prices[i], close_prices[i]) - np.random.rand() * 50

    close_prices = np.clip(close_prices, 10000, 30000)
    open_prices = np.clip(open_prices, low_prices, high_prices)
    high_prices = np.maximum(high_prices, np.maximum(open_prices, close_prices))
    low_prices = np.minimum(low_prices, np.minimum(open_prices, close_prices))

    data = {
        'Timestamp': pd.to_datetime(dates),
        'Open': open_prices, 'High': high_prices,
        'Low': low_prices, 'Close': close_prices,
        'Volume': volume_data
    }
    sample_df = pd.DataFrame(data)
    return sample_df.iloc[-days:]


if __name__ == '__main__':
    SYMBOL = "BTCUSDT"
    INTERVAL = "1d"
    DATA_LIMIT = 200

    SMA_SHORT_PERIOD = 10 # Example, not tuned in this loop
    SMA_LONG_PERIOD = 20  # Example, not tuned in this loop
    BACKTEST_COMMISSION = 0.001 # 0.1% commission per trade side

    # --- Parameter Tuning Setup ---
    rsi_length_values_to_tune = [10, 14, 20, 30, 50]

    output_chart_dir = "tuning_charts_with_backtest"
    if not os.path.exists(output_chart_dir):
        os.makedirs(output_chart_dir)
        print(f"Created directory: {output_chart_dir}")
    else:
        print(f"Directory {output_chart_dir} already exists.")
    # --- End Parameter Tuning Setup ---

    # Base parameters for the custom indicator
    base_custom_indicator_params = {
        "nwe_src_column": "Close", "nwe_h": 8.0, "nwe_mult": 3.0, "nwe_window_size": 50,
        "pt_ma_type": "ema", "pt_ma_length": 20, "pt_bands_dist": 2.0, "pt_atr_length": 14, # Added pt_atr_length
        "pt_band2_mult": 1.5, "pt_band3_mult": 3.0, "pt_confirm_bars": 1,
        "macd_fast_length": 12, "macd_slow_length": 26, "macd_signal_length": 9,
        # "rsi_length": 14, # Will be set by the loop
        "dmi_length": 14, "useDmi": True
    }

    all_results = []

    # --- Main Loop for Parameter Tuning ---
    for rsi_len in rsi_length_values_to_tune:

        current_loop_params = base_custom_indicator_params.copy()
        current_loop_params["rsi_length"] = rsi_len

        param_str = f"rsi_length={rsi_len}"
        print(f"\n--- Running for Parameters: {param_str} ---")

        print(f"Attempting to fetch {DATA_LIMIT} recent {INTERVAL} klines for {SYMBOL}...")
        market_data_raw = fetch_binance_klines(symbol=SYMBOL, interval=INTERVAL, limit=DATA_LIMIT)
        data_for_indicators = None # Renamed for clarity
        plot_title_suffix = ""

        min_data_threshold = DATA_LIMIT * 0.8 # Require at least 80% of requested data
        if market_data_raw is not None and not market_data_raw.empty and len(market_data_raw) >= min_data_threshold:
            print(f"Successfully fetched {len(market_data_raw)} records for {SYMBOL}.")
            data_for_indicators = market_data_raw # Already has Timestamp index
        else:
            if market_data_raw is not None and not market_data_raw.empty:
                 print(f"Fetched only {len(market_data_raw)} records (less than {min_data_threshold:.0f}). Using sample data.")
            else:
                 print(f"Failed to fetch live market data for {SYMBOL} or no data returned.")
            print("Attempting to use sample data instead.")
            data_for_indicators = get_sample_data(days=DATA_LIMIT)
            plot_title_suffix = "_Sample"
            # Ensure Timestamp is index for sample data
            if 'Timestamp' in data_for_indicators.columns:
                 data_for_indicators = data_for_indicators.set_index(pd.to_datetime(data_for_indicators['Timestamp']))

        final_df_for_plotting = None
        plotly_additional_traces = []

        if data_for_indicators is not None and not data_for_indicators.empty:
            # Ensure data_for_indicators has DatetimeIndex
            if not isinstance(data_for_indicators.index, pd.DatetimeIndex):
                # This case should ideally be handled by the data fetching/sampling logic above
                print("Error: data_for_indicators does not have DatetimeIndex. Attempting to set 'Timestamp'.")
                if 'Timestamp' in data_for_indicators.columns:
                    data_for_indicators.set_index(pd.to_datetime(data_for_indicators['Timestamp']), inplace=True)
                elif data_for_indicators.index.name == 'Timestamp' and isinstance(data_for_indicators.index, pd.RangeIndex): # common if reset_index then forget set_index
                    data_for_indicators.index = pd.to_datetime(data_for_indicators.index) # This line is probably wrong, needs actual timestamp data
                else:
                    print("Critical Error: Cannot ensure DatetimeIndex. Skipping this iteration.")
                    data_for_indicators = None

            # Min data length check for indicators (e.g. NWE window)
            min_rows_for_calc = max(base_custom_indicator_params["nwe_window_size"],
                                    current_loop_params.get("pt_ma_length", 20),
                                    base_custom_indicator_params.get("pt_atr_length",14)) + 1 # +1 for diffs etc.

            if data_for_indicators is not None and len(data_for_indicators) >= min_rows_for_calc:
                # Calculate SMAs
                if len(data_for_indicators) >= SMA_SHORT_PERIOD:
                    sma_short = data_for_indicators['Close'].rolling(window=SMA_SHORT_PERIOD).mean()
                    plotly_additional_traces.append(go.Scatter(x=sma_short.index, y=sma_short, mode='lines', name=f'SMA {SMA_SHORT_PERIOD}', line=dict(color='blue', width=0.7)))
                if len(data_for_indicators) >= SMA_LONG_PERIOD:
                    sma_long = data_for_indicators['Close'].rolling(window=SMA_LONG_PERIOD).mean()
                    plotly_additional_traces.append(go.Scatter(x=sma_long.index, y=sma_long, mode='lines', name=f'SMA {SMA_LONG_PERIOD}', line=dict(color='orange', width=0.7)))


                print("\nCalculating custom indicator with current loop params...")
                df_with_indicators, custom_signals_dict = calculate_custom_indicator(data_for_indicators, **current_loop_params)

                print("\nRunning backtest...")
                backtest_metrics = run_backtest(df_with_indicators, commission_per_trade=BACKTEST_COMMISSION)
                print(f"Backtest Results for {param_str}:")
                print(f"  Number of Trades: {backtest_metrics['num_trades']}")
                print(f"  Success Rate: {backtest_metrics['success_rate']}%")
                print(f"  Total P&L (sum of trade %): {backtest_metrics['total_pnl_percentage']}%")
                all_results.append({'params': param_str, 'metrics': backtest_metrics, 'loop_params': current_loop_params.copy()})

                # Add indicator traces for plotting
                if 'nwe_upper' in df_with_indicators.columns:
                    plotly_additional_traces.append(go.Scatter(x=df_with_indicators.index, y=df_with_indicators['nwe_upper'], mode='lines', name='NWE Upper', line=dict(color='rgba(0,128,0,0.7)', dash='dot', width=1)))
                if 'nwe_lower' in df_with_indicators.columns:
                    plotly_additional_traces.append(go.Scatter(x=df_with_indicators.index, y=df_with_indicators['nwe_lower'], mode='lines', name='NWE Lower', line=dict(color='rgba(255,0,0,0.7)', dash='dot', width=1)))
                if 'pt_band1' in df_with_indicators.columns and 'pt_trend' in df_with_indicators.columns:
                    bull_segments = df_with_indicators[df_with_indicators['pt_trend'] == True]
                    bear_segments = df_with_indicators[df_with_indicators['pt_trend'] == False]
                    if not bull_segments.empty:
                        plotly_additional_traces.append(go.Scatter(x=bull_segments.index, y=bull_segments['pt_band1'], mode='lines', name='PT Bull', line=dict(color='rgba(0, 200, 0, 0.8)', width=2)))
                    if not bear_segments.empty:
                        plotly_additional_traces.append(go.Scatter(x=bear_segments.index, y=bear_segments['pt_band1'], mode='lines', name='PT Bear', line=dict(color='rgba(200, 0, 0, 0.8)', width=2)))

                final_df_for_plotting = df_with_indicators

                if final_df_for_plotting is not None and not final_df_for_plotting.empty:
                    chart_title = f"{SYMBOL}{plot_title_suffix} ({param_str} | Trades: {backtest_metrics['num_trades']}, SR: {backtest_metrics['success_rate']}%)"
                    print(f"Creating Plotly chart for {param_str}...")
                    fig = plot_candlestick_chart_plotly(final_df_for_plotting,
                                                    symbol=chart_title,
                                                    interval=INTERVAL,
                                                    additional_traces=plotly_additional_traces)
                    if fig:
                        print(f"Plotly figure object created for {param_str}.")
                        # chart_filename = os.path.join(output_chart_dir, f"chart_{param_str.replace('=', '_').replace(',', '_')}.html")
                        # fig.write_html(chart_filename)
                        # print(f"Chart saved to {chart_filename}")
                    else:
                        print(f"Failed to create Plotly figure for {param_str}.")
                else:
                    print(f"DataFrame for plotting is None or empty for {param_str}.")
            else: # Not enough data after fetching/sampling
                print(f"Not enough data to process for {param_str} (Need at least {min_rows_for_calc} bars, got {len(data_for_indicators) if data_for_indicators is not None else 0}).")
        else: # data_for_indicators is None or empty initially
            print(f"No data available to plot for {param_str}.")
        print(f"--- Finished for Parameters: {param_str} ---")

    print("\n--- Main application finished all tuning loops ---")
    print("\n--- Overall Backtest Results Summary (Sorted by Total P&L %) ---")
    # Sort by total_pnl_percentage for this example
    for result in sorted(all_results, key=lambda x: x['metrics']['total_pnl_percentage'], reverse=True):
        print(f"Params: {result['params']} -> Trades: {result['metrics']['num_trades']}, SR: {result['metrics']['success_rate']}%, Total P&L%: {result['metrics']['total_pnl_percentage']}%")
