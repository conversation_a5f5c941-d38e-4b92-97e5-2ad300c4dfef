# tradingview_backtester/backtester.py
import pandas as pd
import numpy as np

def run_backtest(df: pd.DataFrame, initial_capital: float = 10000.0, commission_per_trade: float = 0.000): # Commission as decimal, e.g. 0.001 for 0.1%
    """
    Runs a simple backtest based on buy/sell signals in the DataFrame.

    Args:
        df (pd.DataFrame): DataFrame with OHLC data (must include 'Close')
                           and a 'signal' column (1 for long, -1 for sell/exit long, 0 for neutral).
                           Assumes 'Close' prices are used for entry/exit.
                           Index should be DatetimeIndex.
        initial_capital (float): Starting capital (currently not used for P&L calc, but for future).
        commission_per_trade (float): Commission fee per trade (e.g., 0.001 for 0.1%).

    Returns:
        dict: A dictionary containing backtest metrics.
              Returns None if essential columns ('Close', 'signal') are missing.
    """
    if 'Close' not in df.columns or 'signal' not in df.columns:
        print("Error in backtester: DataFrame must contain 'Close' and 'signal' columns.")
        return {
            'num_trades': 0,
            'num_profitable_trades': 0,
            'success_rate': 0,
            'total_pnl_percentage': 0, # Sum of percentage P/L per trade
            'trades_log': []
        }

    in_position = False
    entry_price = 0.0
    entry_time = None

    num_trades = 0
    num_profitable_trades = 0
    total_pnl_percentage = 0.0 # Sum of individual trade P/L percentages

    trades_log = []

    print(f"Running backtest on {len(df)} bars...")

    for i in range(len(df)):
        current_bar = df.iloc[i]
        current_time = df.index[i]
        current_signal = current_bar['signal']
        current_close = current_bar['Close']

        if pd.isna(current_signal) or pd.isna(current_close):
            # print(f"Skipping bar {current_time} due to NaN signal or close.")
            continue # Skip bars with NaN data relevant to trading

        if not in_position and current_signal == 1: # Buy signal
            in_position = True
            entry_price = current_close
            entry_time = current_time
            trades_log.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'type': 'long',
                'status': 'open'
            })
            # print(f"{current_time}: Entered LONG @ {entry_price:.2f}")

        elif in_position and current_signal == -1: # Sell signal
            exit_price = current_close

            trade_pnl_percent = ((exit_price - entry_price) / entry_price) * 100
            trade_pnl_percent -= (commission_per_trade * 100 * 2)

            if trade_pnl_percent > 0:
                num_profitable_trades += 1

            total_pnl_percentage += trade_pnl_percent
            num_trades += 1

            if trades_log and trades_log[-1]['status'] == 'open':
                trades_log[-1].update({
                    'exit_time': current_time,
                    'exit_price': exit_price,
                    'pnl_percentage': trade_pnl_percent,
                    'status': 'closed'
                })
            # print(f"{current_time}: Exited LONG @ {exit_price:.2f}, P&L: {trade_pnl_percent:.2f}%")

            in_position = False
            entry_price = 0.0
            entry_time = None


    success_rate = (num_profitable_trades / num_trades) * 100 if num_trades > 0 else 0

    metrics = {
        'num_trades': num_trades,
        'num_profitable_trades': num_profitable_trades,
        'success_rate': round(success_rate, 2),
        'total_pnl_percentage': round(total_pnl_percentage, 2),
        'trades_log': trades_log
    }

    print(f"Backtest complete. Trades: {num_trades}, Profitable: {num_profitable_trades}, Success Rate: {metrics['success_rate']}%, Total P&L %: {metrics['total_pnl_percentage']}%")
    return metrics

if __name__ == '__main__':
    print("Testing backtester.py...")
    # Create sample data
    sample_ohlc_data = {
        'Timestamp': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05',
                                     '2023-01-06', '2023-01-07', '2023-01-08', '2023-01-09', '2023-01-10']),
        'Open':  [100, 102, 101, 99, 103, 105, 107, 106, 108, 110],
        'High':  [103, 104, 102, 101, 106, 108, 109, 108, 110, 112],
        'Low':   [99,  101, 100, 98,  102, 104, 105, 105, 107, 109],
        'Close': [102, 101, 100, 103, 105, 107, 106, 108, 110, 109]
    }
    test_df = pd.DataFrame(sample_ohlc_data).set_index('Timestamp')

    # Test case 1: Profitable sequence
    test_df['signal'] = [1, 0, 0, -1, 1, 0, -1, 0, 1, -1]
    print("\n--- Test Case 1: Profitable Sequence (No Commission) ---")
    metrics1 = run_backtest(test_df.copy())
    # Expected: 3 trades.
    # T1: 102 -> 103 (Profit: (103-102)/102 * 100 = 0.98%)
    # T2: 103 -> 107 (Profit: (107-103)/103 * 100 = 3.88%)
    # T3: 108 -> 109 (Profit: (109-108)/108 * 100 = 0.93%)
    # All 3 should be profitable. Success Rate 100%. Total P&L = 0.98 + 3.88 + 0.93 = 5.79%

    # Test case 2: Mixed sequence
    test_df['signal'] = [1, 0, -1, 1, 0, -1, 1, 0, 0, -1]
    print("\n--- Test Case 2: Mixed Sequence (No Commission) ---")
    metrics2 = run_backtest(test_df.copy())
    # Expected: 3 trades.
    # T1: 102 -> 100 (Loss: (100-102)/102*100 = -1.96%)
    # T2: 103 -> 107 (Profit: 3.88%)
    # T3: 106 -> 109 (Profit: (109-106)/106*100 = 2.83%)
    # 2 profitable, 1 loss. Success Rate ~66.67%. Total P&L = -1.96 + 3.88 + 2.83 = 4.75%

    # Test case 3: No trades
    test_df['signal'] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    print("\n--- Test Case 3: No Trades ---")
    metrics3 = run_backtest(test_df.copy())
    # Expected: 0 trades. Success Rate 0%. Total P&L 0%

    # Test case 4: Signal at the end, position remains open
    test_df['signal'] = [0, 0, 0, 0, 0, 0, 0, 0, 1, 0]
    print("\n--- Test Case 4: Position remains open ---")
    metrics4 = run_backtest(test_df.copy())
    # Expected: 0 completed trades. Success Rate 0%. Total P&L 0%

    # Test case 5: With commission
    test_df['signal'] = [1, 0, 0, -1, 1, 0, -1, 0, 1, -1] # Same as Test Case 1 signals
    print("\n--- Test Case 5: Profitable Sequence (WITH 1% Commission per trade side, so 2% total per trade) ---")
    metrics5 = run_backtest(test_df.copy(), commission_per_trade=0.01)
    # T1: 0.98% - 2% = -1.02% (Loss)
    # T2: 3.88% - 2% =  1.88% (Profit)
    # T3: 0.93% - 2% = -1.07% (Loss)
    # 1 profitable. Success Rate ~33.33%. Total P&L = -1.02 + 1.88 - 1.07 = -0.21%

    print("\n--- Test Case 6: Missing signal column ---")
    metrics6 = run_backtest(test_df[['Close']].copy()) # Missing 'signal'

    print("\n--- Test Case 7: Signal but no close data (NaN) ---")
    test_df_nan = test_df.copy() # Start with a clean copy for this test
    test_df_nan['signal'] = [1,0,-1,0,0,0,0,0,0,0] # Define signal for this test
    test_df_nan.loc[test_df_nan.index[2], 'Close'] = np.nan # Introduce NaN at the exit bar for first trade
    metrics7 = run_backtest(test_df_nan)
    # Expected: T1 entry at 102. Exit attempt at bar 2 (Close=NaN) will be skipped.
    # The trade remains open because the -1 signal at bar 2 cannot be processed.
    # So, 0 completed trades.
    # Log for first trade will show 'open' status.
    print(f"Trade log for Test 7: {metrics7['trades_log']}")
