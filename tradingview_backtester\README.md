# 📈 Enhanced Trading Application - TradingView-Like Backtester

## 🎯 Overview

This is a comprehensive Python-based trading application designed to improve TradingView indicator accuracy through advanced backtesting and parameter optimization. The application provides a complete trading analysis suite with multiple data sources, enhanced indicators, sophisticated backtesting, and an interactive dashboard.

### 🚀 Key Features

- **Multi-Source Data Fetching**: Binance, Yahoo Finance, CoinGecko with intelligent fallback
- **Advanced Technical Indicators**: 15+ indicators including custom TradingView-inspired ones
- **Enhanced Backtesting Engine**: Position sizing, stop-loss, take-profit, slippage modeling
- **Parameter Optimization**: Grid search, random search, genetic algorithms
- **Interactive Dashboard**: Real-time parameter adjustment and visualization
- **Multi-Timeframe Analysis**: Higher timeframe confluence and signal validation
- **Comprehensive Metrics**: Sharpe ratio, profit factor, maximum drawdown, and more

## 🔧 Enhanced Features

### 📊 Data Management
- **Multiple Data Sources**: Binance API, Yahoo Finance, CoinGecko with automatic fallback
- **Data Validation**: Comprehensive data quality checks and cleaning
- **Caching System**: Intelligent caching to reduce API calls and improve performance
- **Historical Data**: Support for extensive historical data analysis

### 📈 Technical Indicators
- **Custom Indicators**:
  - Nadaraya-Watson Envelope (NWE) - Advanced regression-based envelope
  - Perfect Trail - Trend-following bands with dynamic adjustment
  - Enhanced MACD with signal line and histogram
  - RSI with customizable periods and levels
  - DMI/ADX for trend strength analysis

- **Enhanced Indicators**:
  - Bollinger Bands with %B and bandwidth
  - Stochastic Oscillator (%K and %D)
  - Williams %R
  - SuperTrend with dynamic multipliers
  - On-Balance Volume (OBV)
  - Volume Weighted Average Price (VWAP)
  - Money Flow Index (MFI)
  - Ichimoku Cloud components
  - Pivot Points and Fibonacci retracements

### 💰 Advanced Backtesting
- **Position Management**: Multiple position sizing methods (fixed amount, percentage, Kelly criterion)
- **Risk Management**: Stop-loss and take-profit with customizable levels
- **Realistic Trading Costs**: Commission, slippage, and spread modeling
- **Advanced Metrics**: Sharpe ratio, profit factor, maximum drawdown, win rate
- **Equity Curve Analysis**: Real-time portfolio value tracking
- **Trade Analytics**: Detailed trade-by-trade analysis and statistics

### 🎯 Parameter Optimization
- **Multiple Algorithms**: Grid search, random search, genetic algorithms
- **Walk-Forward Analysis**: Out-of-sample testing with rolling windows
- **Parameter Sensitivity**: Analysis of parameter impact on performance
- **Overfitting Detection**: Statistical validation of optimization results
- **Multi-Objective Optimization**: Balance between return and risk metrics

## 📁 Project Structure

```
tradingview_backtester/
├── 📊 Core Modules
│   ├── enhanced_main.py              # Main application entry point
│   ├── enhanced_data_fetcher.py      # Multi-source data fetching
│   ├── enhanced_backtester.py        # Advanced backtesting engine
│   ├── enhanced_indicators.py        # Technical indicators library
│   ├── parameter_optimizer.py        # Optimization algorithms
│   └── config.py                     # Configuration management
│
├── 🎨 Visualization
│   ├── dashboard.py                  # Interactive Streamlit dashboard
│   ├── chart_plotter.py             # Plotly chart generation
│   └── run_dashboard.py             # Dashboard launcher script
│
├── 🔧 Legacy/Original
│   ├── main.py                      # Original main script
│   ├── data_fetcher.py              # Basic Binance API fetcher
│   ├── backtester.py                # Basic backtesting
│   └── custom_indicator.py          # Custom TradingView indicators
│
├── 📋 Configuration
│   ├── requirements.txt             # Python dependencies
│   ├── setup.py                     # Package setup script
│   └── README.md                    # This documentation
│
└── 📁 Output Directories
    └── tuning_charts_with_backtest/  # Generated charts and results
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Installation

1. **Clone or download the repository**
   ```bash
   git clone <repository-url>
   cd tradingview_backtester
   ```

2. **Create a virtual environment (recommended)**
   ```bash
   python -m venv trading_env

   # Activate the environment
   # Windows:
   trading_env\Scripts\activate

   # macOS/Linux:
   source trading_env/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Verify installation**
   ```bash
   python -c "import pandas, numpy, plotly, streamlit; print('✅ All dependencies installed successfully')"
   ```

## 🎮 Usage Guide

### 🖥️ Interactive Dashboard (Recommended)

Launch the interactive dashboard for real-time analysis:

```bash
python run_dashboard.py
```

Or directly with Streamlit:
```bash
streamlit run dashboard.py
```

The dashboard will be available at `http://localhost:8501`

**Dashboard Features:**
- 📊 Real-time parameter adjustment
- 📈 Interactive charts with multiple indicators
- 💰 Live backtesting results
- 🎯 Parameter optimization tools
- 📋 Detailed trade analysis

### 🖥️ Command Line Interface

For programmatic analysis, use the enhanced main application:

```bash
python enhanced_main.py
```

**Configuration Options:**
- Edit `config.py` for default settings
- Modify parameters in the dashboard sidebar
- Use environment variables for deployment settings

### 📊 Basic Analysis Script

For simple backtesting with the original functionality:

```bash
python main.py
```

This runs the original parameter tuning loop with basic indicators.

## Output

For each parameter combination in the tuning loop:
*   Console output details parameters, data status, **backtest performance metrics**, and chart creation status.
*   A final summary table in the console ranks parameter sets by success rate or P&L.
*   If `fig.show()` or `fig.write_html()` is enabled in `main.py` (for local runs):
    *   An interactive Plotly chart displays candlesticks (colored by indicator trend), volume, SMAs, custom indicator overlays (NWE, Perfect Trail, MACD), and buy/sell signal markers.
    *   Chart titles include a summary of backtest results for that parameter set.

## Future Enhancements
*   Implement Phase 2 of the custom indicator (ML/RL emulation, advanced sentiment/volume/swing analysis, full MTF).
*   More sophisticated backtesting: different order types, position sizing, stop-loss/take-profit, portfolio-level stats.
*   User input for parameters at runtime.
*   Date range selection for backtesting.
*   More advanced parameter optimization techniques.
```
