"""
Interactive Trading Dashboard using Streamlit
Real-time parameter adjustment and backtesting visualization
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import enhanced modules
from enhanced_data_fetcher import EnhancedDataFetcher
from enhanced_backtester import EnhancedBacktester, BacktestConfig
from enhanced_indicators import EnhancedIndicators
from custom_indicator import calculate_custom_indicator

# Page configuration
st.set_page_config(
    page_title="Enhanced Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-metric {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }
    .warning-metric {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
    }
    .danger-metric {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)  # Cache for 5 minutes
def load_data(symbol, interval, limit, sources):
    """Load and cache data"""
    fetcher = EnhancedDataFetcher()
    return fetcher.fetch_data_with_fallback(symbol, interval, limit, sources)

@st.cache_data(ttl=60)  # Cache for 1 minute
def calculate_indicators_cached(df, config):
    """Calculate indicators with caching"""
    indicators = EnhancedIndicators()
    enhanced_df = indicators.calculate_all_indicators(df, config)
    
    # Add custom indicators
    if 'custom_indicator_params' in config:
        custom_df, signals = calculate_custom_indicator(enhanced_df, **config['custom_indicator_params'])
        return custom_df
    
    return enhanced_df

def create_dashboard():
    """Main dashboard function"""
    
    # Header
    st.markdown('<h1 class="main-header">📈 Enhanced Trading Dashboard</h1>', unsafe_allow_html=True)
    
    # Sidebar for parameters
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Data source settings
        st.subheader("📊 Data Source")
        symbol = st.selectbox(
            "Trading Pair",
            ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"],
            index=0
        )
        
        interval = st.selectbox(
            "Timeframe",
            ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"],
            index=6  # Default to 1d
        )
        
        limit = st.slider("Data Points", 50, 1000, 200)
        
        sources = st.multiselect(
            "Data Sources",
            ["binance", "yahoo", "coingecko"],
            default=["binance", "yahoo"]
        )
        
        # Indicator parameters
        st.subheader("🔧 Indicator Parameters")
        
        # Custom indicator parameters
        with st.expander("Custom Indicators"):
            rsi_length = st.slider("RSI Length", 5, 50, 14)
            pt_ma_length = st.slider("Perfect Trail MA Length", 5, 100, 20)
            macd_fast = st.slider("MACD Fast", 5, 20, 12)
            macd_slow = st.slider("MACD Slow", 15, 35, 26)
            macd_signal = st.slider("MACD Signal", 5, 15, 9)
        
        # Enhanced indicator parameters
        with st.expander("Enhanced Indicators"):
            bb_length = st.slider("Bollinger Bands Length", 10, 50, 20)
            bb_std = st.slider("Bollinger Bands Std Dev", 1.0, 3.0, 2.0, 0.1)
            stoch_k = st.slider("Stochastic %K", 5, 30, 14)
            stoch_d = st.slider("Stochastic %D", 2, 10, 3)
            supertrend_period = st.slider("SuperTrend Period", 5, 20, 10)
            supertrend_mult = st.slider("SuperTrend Multiplier", 1.0, 5.0, 3.0, 0.1)
        
        # Backtesting parameters
        st.subheader("💰 Backtesting")
        initial_capital = st.number_input("Initial Capital", 1000, 100000, 10000)
        commission_rate = st.slider("Commission Rate (%)", 0.0, 1.0, 0.1, 0.01) / 100
        slippage_rate = st.slider("Slippage Rate (%)", 0.0, 0.5, 0.05, 0.01) / 100
        
        use_stop_loss = st.checkbox("Use Stop Loss")
        stop_loss_pct = st.slider("Stop Loss (%)", 1, 20, 5) / 100 if use_stop_loss else None
        
        use_take_profit = st.checkbox("Use Take Profit")
        take_profit_pct = st.slider("Take Profit (%)", 1, 50, 10) / 100 if use_take_profit else None
        
        # Action buttons
        st.subheader("🚀 Actions")
        run_analysis = st.button("Run Analysis", type="primary")
        auto_refresh = st.checkbox("Auto Refresh (30s)")
    
    # Main content area
    if run_analysis or auto_refresh:
        
        # Progress indicator
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            # Load data
            status_text.text("📊 Loading data...")
            progress_bar.progress(20)
            
            df = load_data(symbol, interval, limit, sources)
            
            if df is None or df.empty:
                st.error("❌ Failed to load data. Please check your settings.")
                return
            
            # Prepare configuration
            config = {
                'custom_indicator_params': {
                    'rsi_length': rsi_length,
                    'pt_ma_length': pt_ma_length,
                    'macd_fast_length': macd_fast,
                    'macd_slow_length': macd_slow,
                    'macd_signal_length': macd_signal,
                    'pt_ma_type': 'ema',
                    'pt_atr_length': 14,
                    'pt_bands_dist': 2.0,
                    'nwe_h': 8.0,
                    'nwe_mult': 3.0,
                    'nwe_window_size': 50,
                    'dmi_length': 14,
                    'useDmi': True
                },
                'bb_length': bb_length,
                'bb_std': bb_std,
                'stoch_k': stoch_k,
                'stoch_d': stoch_d,
                'supertrend_period': supertrend_period,
                'supertrend_mult': supertrend_mult
            }
            
            # Calculate indicators
            status_text.text("🔧 Calculating indicators...")
            progress_bar.progress(50)
            
            enhanced_df = calculate_indicators_cached(df, config)
            
            # Run backtest
            status_text.text("💰 Running backtest...")
            progress_bar.progress(70)
            
            backtest_config = BacktestConfig(
                initial_capital=initial_capital,
                commission_rate=commission_rate,
                slippage_rate=slippage_rate,
                stop_loss_percentage=stop_loss_pct,
                take_profit_percentage=take_profit_pct
            )
            
            backtester = EnhancedBacktester(backtest_config)
            results = backtester.run_backtest(enhanced_df)
            
            # Create visualizations
            status_text.text("📈 Creating charts...")
            progress_bar.progress(90)
            
            # Clear progress indicators
            progress_bar.empty()
            status_text.empty()
            
            # Display results
            display_results(enhanced_df, results, symbol)
            
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")
            progress_bar.empty()
            status_text.empty()
    
    else:
        # Welcome message
        st.info("👈 Configure your parameters in the sidebar and click 'Run Analysis' to start!")
        
        # Show sample data info
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("📊 Data Sources", "3", "Binance, Yahoo, CoinGecko")
        
        with col2:
            st.metric("🔧 Indicators", "15+", "Technical & Custom")
        
        with col3:
            st.metric("🎯 Optimization", "Advanced", "GA, Grid, Random Search")

def display_results(df, backtest_results, symbol):
    """Display analysis results"""
    
    # Key metrics
    st.subheader("📊 Key Metrics")
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        pnl_color = "success" if backtest_results['total_pnl'] > 0 else "danger"
        st.markdown(f"""
        <div class="metric-card {pnl_color}-metric">
            <h4>Total P&L</h4>
            <h2>${backtest_results['total_pnl']:.2f}</h2>
            <p>{backtest_results['total_pnl_percentage']:.2f}%</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        success_color = "success" if backtest_results['success_rate'] > 50 else "warning"
        st.markdown(f"""
        <div class="metric-card {success_color}-metric">
            <h4>Success Rate</h4>
            <h2>{backtest_results['success_rate']:.1f}%</h2>
            <p>{backtest_results['profitable_trades']}/{backtest_results['total_trades']} trades</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        drawdown_color = "success" if backtest_results['max_drawdown'] < 10 else "warning"
        st.markdown(f"""
        <div class="metric-card {drawdown_color}-metric">
            <h4>Max Drawdown</h4>
            <h2>{backtest_results['max_drawdown']:.1f}%</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        sharpe_color = "success" if backtest_results['sharpe_ratio'] > 1 else "warning"
        st.markdown(f"""
        <div class="metric-card {sharpe_color}-metric">
            <h4>Sharpe Ratio</h4>
            <h2>{backtest_results['sharpe_ratio']:.2f}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col5:
        pf_color = "success" if backtest_results['profit_factor'] > 1.5 else "warning"
        st.markdown(f"""
        <div class="metric-card {pf_color}-metric">
            <h4>Profit Factor</h4>
            <h2>{backtest_results['profit_factor']:.2f}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    # Charts
    st.subheader("📈 Price Chart & Indicators")
    
    # Create main chart
    fig = create_main_chart(df, symbol)
    st.plotly_chart(fig, use_container_width=True)
    
    # Additional charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 Oscillators")
        osc_fig = create_oscillator_chart(df)
        st.plotly_chart(osc_fig, use_container_width=True)
    
    with col2:
        st.subheader("💰 Equity Curve")
        if backtest_results['equity_curve']:
            equity_fig = create_equity_chart(backtest_results['equity_curve'])
            st.plotly_chart(equity_fig, use_container_width=True)
    
    # Trade details
    if backtest_results['trades']:
        st.subheader("📋 Trade Details")
        trades_df = pd.DataFrame(backtest_results['trades'])
        st.dataframe(trades_df, use_container_width=True)

def create_main_chart(df, symbol):
    """Create main price chart with indicators"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.03,
        subplot_titles=[f'{symbol} Price & Indicators', 'Volume'],
        row_heights=[0.8, 0.2]
    )
    
    # Candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name='Price'
        ),
        row=1, col=1
    )
    
    # Add indicators
    indicators_to_plot = [
        ('bb_upper', 'Bollinger Upper', 'blue'),
        ('bb_lower', 'Bollinger Lower', 'blue'),
        ('supertrend', 'SuperTrend', 'purple'),
        ('vwap', 'VWAP', 'orange')
    ]
    
    for indicator, name, color in indicators_to_plot:
        if indicator in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df[indicator],
                    mode='lines',
                    name=name,
                    line=dict(color=color, width=1)
                ),
                row=1, col=1
            )
    
    # Add signals
    if 'signal' in df.columns:
        buy_signals = df[df['signal'] == 1]
        sell_signals = df[df['signal'] == -1]
        
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals.index,
                    y=buy_signals['Low'] * 0.98,
                    mode='markers',
                    name='Buy Signal',
                    marker=dict(symbol='triangle-up', size=8, color='green')
                ),
                row=1, col=1
            )
        
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals.index,
                    y=sell_signals['High'] * 1.02,
                    mode='markers',
                    name='Sell Signal',
                    marker=dict(symbol='triangle-down', size=8, color='red')
                ),
                row=1, col=1
            )
    
    # Volume
    if 'Volume' in df.columns:
        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['Volume'],
                name='Volume',
                marker_color='rgba(150,150,150,0.4)'
            ),
            row=2, col=1
        )
    
    fig.update_layout(
        title=f'{symbol} Trading Analysis',
        xaxis_rangeslider_visible=False,
        height=600
    )
    
    return fig

def create_oscillator_chart(df):
    """Create oscillator indicators chart"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=['RSI', 'Stochastic']
    )
    
    # RSI
    if 'rsi' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['rsi'],
                mode='lines',
                name='RSI',
                line=dict(color='purple')
            ),
            row=1, col=1
        )
        
        # RSI levels
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=1, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=1, col=1)
    
    # Stochastic
    if 'stoch_k' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['stoch_k'],
                mode='lines',
                name='%K',
                line=dict(color='blue')
            ),
            row=2, col=1
        )
    
    if 'stoch_d' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['stoch_d'],
                mode='lines',
                name='%D',
                line=dict(color='red')
            ),
            row=2, col=1
        )
    
    # Stochastic levels
    fig.add_hline(y=80, line_dash="dash", line_color="red", row=2, col=1)
    fig.add_hline(y=20, line_dash="dash", line_color="green", row=2, col=1)
    
    fig.update_layout(height=400)
    return fig

def create_equity_chart(equity_curve):
    """Create equity curve chart"""
    equity_df = pd.DataFrame(equity_curve)
    
    fig = go.Figure()
    
    fig.add_trace(
        go.Scatter(
            x=equity_df['timestamp'],
            y=equity_df['equity'],
            mode='lines',
            name='Equity',
            line=dict(color='blue', width=2)
        )
    )
    
    fig.update_layout(
        title='Portfolio Equity Curve',
        xaxis_title='Date',
        yaxis_title='Equity ($)',
        height=400
    )
    
    return fig

if __name__ == "__main__":
    # Auto-refresh functionality
    if st.sidebar.checkbox("Auto Refresh (30s)"):
        import time
        time.sleep(30)
        st.experimental_rerun()
    
    create_dashboard()
