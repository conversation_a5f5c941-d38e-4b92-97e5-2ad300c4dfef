import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def plot_candlestick_chart_plotly(df: pd.DataFrame, symbol: str = "", interval: str = "", additional_traces: list = None):
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'Timestamp' in df.columns:
            # Attempt to convert 'Timestamp' column to datetime and set as index
            try:
                df['Timestamp'] = pd.to_datetime(df['Timestamp'])
                df = df.set_index('Timestamp')
            except Exception as e:
                print(f"Error: Could not convert 'Timestamp' column to DatetimeIndex: {e}")
                return None # Return None on failure
        else:
            print("Error: DataFrame must have a 'Timestamp' column or a DatetimeIndex.")
            return None # Return None on failure

    if df.empty:
        print("DataFrame is empty. Cannot plot chart.")
        return None

    required_cols = ['Open', 'High', 'Low', 'Close']
    if not all(col in df.columns for col in required_cols):
        print(f"Error: DataFrame must contain columns: {', '.join(required_cols)}")
        return None

    has_volume = 'Volume' in df.columns and not df['Volume'].isnull().all()
    has_bar_colors = 'bar_plot_color' in df.columns
    has_signals = 'signal' in df.columns # For buy/sell markers

    rows = 2 if has_volume else 1
    row_heights = [0.7, 0.3] if has_volume else [1.0]
    subplot_titles_list = [f'{symbol} {interval} OHLC']
    if has_volume:
        subplot_titles_list.append('Volume')

    fig = make_subplots(rows=rows, cols=1, shared_xaxes=True,
                        vertical_spacing=0.03, subplot_titles=subplot_titles_list,
                        row_heights=row_heights)

    # Candlestick plotting based on 'bar_plot_color'
    if has_bar_colors:
        # Green bars (trend up)
        df_green = df[df['bar_plot_color'] == 'rgba(0,180,0,0.7)']
        if not df_green.empty:
            fig.add_trace(go.Candlestick(x=df_green.index,
                                         open=df_green['Open'], high=df_green['High'],
                                         low=df_green['Low'], close=df_green['Close'],
                                         increasing_line_color='rgba(0,180,0,0.7)', increasing_fillcolor='rgba(0,180,0,0.7)',
                                         decreasing_line_color='rgba(0,180,0,0.7)', decreasing_fillcolor='rgba(0,180,0,0.7)', # Still green even if O > C
                                         name='Trend Up'), row=1, col=1)

        # Red bars (trend down)
        df_red = df[df['bar_plot_color'] == 'rgba(180,0,0,0.7)']
        if not df_red.empty:
            fig.add_trace(go.Candlestick(x=df_red.index,
                                         open=df_red['Open'], high=df_red['High'],
                                         low=df_red['Low'], close=df_red['Close'],
                                         increasing_line_color='rgba(180,0,0,0.7)', increasing_fillcolor='rgba(180,0,0,0.7)', # Still red even if O < C
                                         decreasing_line_color='rgba(180,0,0,0.7)', decreasing_fillcolor='rgba(180,0,0,0.7)',
                                         name='Trend Down'), row=1, col=1)

        # Grey bars (neutral trend)
        df_grey = df[df['bar_plot_color'] == 'rgba(128,128,128,0.5)']
        if not df_grey.empty:
            fig.add_trace(go.Candlestick(x=df_grey.index,
                                         open=df_grey['Open'], high=df_grey['High'],
                                         low=df_grey['Low'], close=df_grey['Close'],
                                         increasing_line_color='rgba(128,128,128,0.5)', increasing_fillcolor='rgba(128,128,128,0.5)',
                                         decreasing_line_color='rgba(128,128,128,0.5)', decreasing_fillcolor='rgba(128,128,128,0.5)',
                                         name='Trend Neutral'), row=1, col=1)
    else:
        # Default candlestick if no bar_plot_color column
        fig.add_trace(go.Candlestick(x=df.index,
                                     open=df['Open'], high=df['High'],
                                     low=df['Low'], close=df['Close'],
                                     name='OHLC',
                                     increasing_line_color='rgba(0,200,0,0.7)', increasing_fillcolor='rgba(0,200,0,0.7)',
                                     decreasing_line_color='rgba(200,0,0,0.7)', decreasing_fillcolor='rgba(200,0,0,0.7)'),
                      row=1, col=1)

    if has_volume:
        fig.add_trace(go.Bar(x=df.index, y=df['Volume'], name='Volume', marker_color='rgba(150,150,150,0.4)'), row=2, col=1)
        fig.update_yaxes(title_text="Volume", row=2, col=1)

    # Add Signal Markers
    if has_signals:
        buy_signals = df[df['signal'] == 1]
        sell_signals = df[df['signal'] == -1]

        if not buy_signals.empty:
            fig.add_trace(go.Scatter(x=buy_signals.index, y=buy_signals['Low'] * 0.98,
                                     mode='markers', name='Buy Signal',
                                     marker=dict(symbol='triangle-up', size=10, color='rgba(0,255,0,0.9)'),
                                     legendgroup="signals", legendgrouptitle_text="Signals")) # Group signals in legend
        if not sell_signals.empty:
            fig.add_trace(go.Scatter(x=sell_signals.index, y=sell_signals['High'] * 1.02,
                                     mode='markers', name='Sell Signal',
                                     marker=dict(symbol='triangle-down', size=10, color='rgba(255,0,0,0.9)'),
                                     legendgroup="signals")) # Group signals in legend

    if additional_traces:
        for trace in additional_traces:
            fig.add_trace(trace, row=1, col=1)

    fig.update_layout(title_text=f'{symbol} {interval} Chart',
                      xaxis_title='Date', yaxis_title='Price',
                      xaxis_rangeslider_visible=False, legend_title_text='Legend')
    fig.update_yaxes(title_text="Price", row=1, col=1)

    print(f"Plotly figure for {symbol} {interval} created with bar colors/signals.")
    return fig

if __name__ == '__main__':
    print("Chart Plotter direct test with bar colors and signals...")
    sample_plot_data = {
        'Timestamp': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05', '2023-01-06']),
        'Open': [100, 102, 101, 103, 105, 104],
        'High': [105, 106, 103, 108, 107, 106],
        'Low': [98, 101, 99, 102, 104, 103],
        'Close': [102, 101, 103, 107, 104, 105],
        'Volume': [1000,1200,800,1500,900,1100],
        'bar_plot_color': ['rgba(0,180,0,0.7)', 'rgba(180,0,0,0.7)', 'rgba(0,180,0,0.7)', 'rgba(0,180,0,0.7)', 'rgba(180,0,0,0.7)', 'rgba(128,128,128,0.5)'], # Added grey for neutral
        'signal': [1, -1, 1, 0, -1, 0] # Added a neutral signal
    }
    test_plot_df = pd.DataFrame(sample_plot_data).set_index('Timestamp')

    # Test with additional traces (e.g., an SMA)
    test_plot_df['SMA_test'] = test_plot_df['Close'].rolling(window=2).mean()
    sma_trace = go.Scatter(x=test_plot_df.index, y=test_plot_df['SMA_test'], mode='lines', name='SMA Test', line=dict(color='blue'))

    fig = plot_candlestick_chart_plotly(test_plot_df, "TestSymbol", "1D", additional_traces=[sma_trace])
    if fig:
        print("Chart plotter test figure created successfully.")
        # fig.show() # Uncomment to display locally
        # fig.write_html("chart_plotter_test.html") # Uncomment to save locally
