import requests
import pandas as pd
from datetime import datetime

def fetch_binance_klines(symbol: str, interval: str, start_time_ms: int = None, end_time_ms: int = None, limit: int = 500):
    """
    Fetches kline/candlestick data from Binance API.

    Args:
        symbol (str): Trading symbol (e.g., "BTCUSDT").
        interval (str): Kline interval (e.g., "1d", "4h", "1h").
        start_time_ms (int, optional): Start time in UNIX milliseconds. Defaults to None.
        end_time_ms (int, optional): End time in UNIX milliseconds. Defaults to None.
        limit (int, optional): Number of data points to retrieve (max 1000). Defaults to 500.

    Returns:
        pandas.DataFrame: DataFrame with OHLCV data, or None if an error occurs.
    """
    base_url = "https://api.binance.com/api/v3/klines"
    params = {
        "symbol": symbol,
        "interval": interval,
        "limit": limit
    }

    if start_time_ms:
        params["startTime"] = start_time_ms
    if end_time_ms:
        params["endTime"] = end_time_ms

    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()  # Raise an exception for bad status codes
        data = response.json()

        # Convert to Pandas DataFrame
        df = pd.DataFrame(data, columns=[
            'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume',
            'CloseTime', 'QuoteAssetVolume', 'NumberOfTrades',
            'TakerBuyBaseAssetVolume', 'TakerBuyQuoteAssetVolume', 'Ignore'
        ])

        # Select and rename essential columns
        df = df[['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']]

        # Convert data types
        df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = pd.to_numeric(df[col])

        df.sort_values(by='Timestamp', inplace=True)
        df.reset_index(drop=True, inplace=True)

        return df

    except requests.exceptions.RequestException as e:
        print(f"API Request Error: {e}")
        return None
    except ValueError as e:
        print(f"Data Processing Error: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

if __name__ == '__main__':
    # Example usage:
    print("Fetching BTCUSDT 1-day klines...")
    # Example: Fetch last 5 days of 1-day klines for BTCUSDT
    # Note: For actual historical range, you'd calculate start_time_ms and end_time_ms
    btc_data = fetch_binance_klines("BTCUSDT", "1d", limit=5)
    if btc_data is not None:
        print(f"Successfully fetched {len(btc_data)} records.")
        print(btc_data.head())
    else:
        print("Failed to fetch data.")

    print("\nFetching ETHUSDT 1-hour klines (last 10)...")
    eth_data = fetch_binance_klines("ETHUSDT", "1h", limit=10)
    if eth_data is not None:
        print(f"Successfully fetched {len(eth_data)} records.")
        print(eth_data.head())
    else:
        print("Failed to fetch data.")

    # Example with a specific start time (timestamp for 2023-01-01 00:00:00 UTC in milliseconds)
    # print("\nFetching BTCUSDT 1-day klines from 2023-01-01...")
    # start_of_2023_ms = int(datetime(2023, 1, 1).timestamp() * 1000)
    # btc_historical_data = fetch_binance_klines("BTCUSDT", "1d", start_time_ms=start_of_2023_ms, limit=5)
    # if btc_historical_data is not None:
    #     print(f"Successfully fetched {len(btc_historical_data)} records from 2023-01-01.")
    #     print(btc_historical_data.head())
    # else:
    #     print("Failed to fetch historical data.")
