o

    �$Qh.G  �                   @   sV  d Z ddlZddlZddlmZmZ ddlmZ	 ddl
mZ ddlZddl
Z
e
�d� ddlmZ ddlmZmZ ddlmZmZ dd	lmZmZ dd
lmZ ddlmZ ddlmZ G d
d� d�Z e!dkr�e � Z"dZ#dZ$dZ%e&d� e&de#� de$� de%� �� e"j'e#e$e%dd�Z(e(r�e"�)e(� e&de*e(d j+�� d�� e&d� dS e&d� dS dS )zs
Enhanced Trading Application Main Entry Point
Integrates all enhanced features for comprehensive trading analysis
�    N)�datetime�	timedelta)�
make_subplots�ignore)�EnhancedDataFetcher)�EnhancedBacktester�BacktestConfig)�ParameterOptimizer�ParameterRange)�EnhancedIndicators�MultiTimeframeAnalysis)�calculate_custom_indicator)�plot_candlestick_chart_plotly)�apply_profitable_signalsc                   @   s�   e Zd ZdZdd� Z		d$deded	ed
edej	f
dd
�Z
d%dej	dedej	fdd�Zd%dej	de
defdd�Z		d&dej	dededefdd�Z	d%dej	dededejfdd�Z		d'deded	ededef
dd �Zd!efd"d#�ZdS )(�TradingApplicationzB
    Enhanced Trading Application with comprehensive features
    c                 C   s"   t � | _t� | _t� | _g | _d S �N)r   �data_fetcherr   �
indicatorsr   �mtf_analysis�results_history)�self� r   �>/mnt/persist/workspace/tradingview_backtester/enhanced_main.py�__init__   s   
zTradingApplication.__init__�1d��  N�symbol�interval�limit�sources�returnc                 C   s�   t d|� d�� | j�||||�}|du rt d� dS | j�|�\}}|s,t d|� �� d|jv r?t|jtj�s?|j	ddd� t d	t
|�� d
|jd � d|jd
 � �� |S )z&Fetch and prepare data with validationu   🔄 Fetching data for z...Nu   ❌ Failed to fetch datau    ⚠️  Data validation issues: �	TimestampT)�inplaceu   ✅ Data prepared: z records from r   � to �����)�printr   �fetch_data_with_fallback�
validate_data�columns�
isinstance�index�pd�
DatetimeIndex�	set_index�len)r   r   r   r   r   �df�is_valid�issuesr   r   r   �fetch_and_prepare_data#   s   *z)TradingApplication.fetch_and_prepare_datar/   �configc                 C   s�   t d� | j�||�}|r d|v r t|fi |d ��\}}|}ndddddddd	d
dddd
d�
}t|fi |��\}}|}t|�}|S )z!Calculate all enhanced indicatorsu'   🔧 Calculating enhanced indicators...�custom_indicator_params�   �   �   �   �	   �ema�       @�       @�      @�2   T�
�
rsi_length�pt_ma_length�macd_fast_length�macd_slow_length�macd_signal_length�
pt_ma_type�
pt_atr_length�
pt_bands_dist�nwe_h�nwe_mult�nwe_window_size�
dmi_length�useDmi)r%   r   �calculate_all_indicatorsr
   r   )r   r/   r3   �enhanced_df�	custom_df�signals�default_paramsr   r   r   �calculate_enhanced_indicators;   s.   �z0TradingApplication.calculate_enhanced_indicatorsc              	   C   s<   t d� |du rtddddddd	d
�}t|�}|�|�}|S )z,Run enhanced backtest with advanced featuresu!   🚀 Running enhanced backtest...N�'  �����MbP?�����Mb@?�fixed_percentageg      .@g{�G�z�?g{�G�z�?��initial_capital�commission_rate�
slippage_rate�position_size_method�position_size_value�stop_loss_percentage�take_profit_percentage)r%   r   r   �run_backtest)r   r/   r3   �
backtester�resultsr   r   r   �run_enhanced_backtest_   s   �

z(TradingApplication.run_enhanced_backtest�random�d   �method�
iterationsc                    s�   t d|� d�� tddddd�tddd	dd�td
dddd�td
dddd�tddddd�tddddd�g}d � �fdd�	}t|d�}|dkrN|j||d�}|S |dkrf|j|td	|d �td|d �d�}|S |�||�}|S )!z)Optimize parameters using various methodsu+   🎯 Starting parameter optimization using z
 method...r@   �
   �   �int)�
param_typerA   r>   rB   �   �   rC   r6   �	bb_length�   �   �bb_stdg      �?g      @g�������?)�stepNc                    s&  |d u r� }zlt | �dd��t | �dd��t | �dd��t | �dd��d	d
dddd
dddd�
t | �dd��| �dd�d�}��||�}tdddddddd�}��||�}|d dk rb|d  d9  < |d  d!krp|d  d"9  < |W S  ty� } ztd#|� �� d$d%d%d%d&�W  Y d }~S d }~ww )'Nr@   r5   rA   r6   rB   r7   rC   r8   r9   r:   r;   r<   r=   r>   Tr?   rm   rp   )r4   rm   rp   rS   rT   rU   rV   g      4@g���Q��?g���Q��?rW   �total_trades�   �total_pnl_percentage�      �?�sharpe_ratiog      �?g333333�?zError in objective function: i����r   )rt   �success_raterr   rv   )ri   �getrR   r   rb   �	Exceptionr%   )�params�data�indicator_config�
enhanced_data�backtest_configra   �e�r/   r   r   r   �objective_function�   sZ   �
��
���zBTradingApplication.optimize_parameters.<locals>.objective_functionrt   �grid)�max_combinations�genetic�   )�population_size�generationsr   )r%   r
   r	   �grid_search�genetic_algorithm�min�max�
random_search)r   r/   re   rf   �parameter_rangesr�   �	optimizerra   r   r�   r   �optimize_parameterss   s*   �

>���z&TradingApplication.optimize_parameters�show_indicatorsc                 C   s&  t d� |du rg d�}tdddd|� d�d	d
dgg d�d
�}d|jv r^dD ]6\}}|d |k}|�� r\|jtj|| j|| d || d || d || d |||||d�
ddd� q&n|jtj|j|d |d |d |d dd�ddd� |D ]}||jv r�|jtj|j|| d|�	� t
dd�d�ddd� qzd	|jv r�|jtj|j|d	 d	dd�ddd� d|jv r�|jtj|j|d ddt
d d!�d�d"dd� d#|jv r�|jtj|j|d# dd$t
d%d!�d�d"dd� d&|jv �r|jtj|j|d& ddt
d'd!�d�ddd� d(|jv �r|jtj|j|d( dd)t
d*d!�d�ddd� d+|jv �rq||d+ dk }	||d+ d,k }
|	j�sS|jtj|	j|	d d- d.d/t
d0d1d2d3�d4�ddd� |
j�sq|jtj|
j|
d d5 d.d6t
d7d1d*d3�d4�ddd� |j
|� d8�d9d:dd;� |jd<d=d*d"dd>� |jd?d=d2d"dd>� |S )@z.Create enhanced chart with multiple indicatorsu   📊 Creating enhanced chart...N)�bb_upper�bb_lower�
supertrend�vwapr�   �   Tg{�G�z�?z Price & Indicators�VolumezRSI & Stochastic�MACD)ru   �333333�?g�������?r�   )�rows�cols�shared_xaxes�vertical_spacing�subplot_titles�row_heights�bar_plot_color))zrgba(0,180,0,0.7)�Bullish)zrgba(180,0,0,0.7)�Bearish�Open�High�Low�Close)
�x�open�high�low�close�name�increasing_line_color�decreasing_line_color�increasing_fillcolor�decreasing_fillcolor)�row�col�Price)r�   r�   r�   r�   r�   r�   �lines)�width)r�   �y�moder�   �linezrgba(150,150,150,0.4))r�   r�   r�   �marker_color�   �rsi�RSI�purple)�color�   �stoch_kzStoch %K�orange�	macd_line�blue�macd_signal_linezMACD Signal�red�signalr$   g\���(\�?�markersz
Buy Signalztriangle-uprg   �green)r   �sizer�   )r�   r�   r�   r�   �markergR���Q�?zSell Signalz
triangle-downz Enhanced Trading AnalysisFi   )�title�xaxis_rangeslider_visible�height�
showlegend�F   �dash)r�   �	line_dash�
line_colorr�   r�   rh   )r%   r   r(   �any�	add_trace�go�Candlestickr*   �Scatter�upper�dict�Bar�empty�
update_layout�	add_hline)r   r/   r   r�   �figr�   r�   �mask�	indicator�buy_signals�sell_signalsr   r   r   �create_enhanced_chart�   s  �
	



������
���
��
��
������
��
���z(TradingApplication.create_enhanced_chartT�optimizec                 C   s�   t d|� �� | �|||�}|du rdS | �|�}| �|�}d}|r*| j|ddd�}| �||�}	|t|�|jd � d|jd � �||rI|dd	� nd|	|d
�}
| j�	|
� |
S )z"Run comprehensive trading analysisu)   🎯 Starting comprehensive analysis for Nrc   r>   )re   rf   r   r#   r$   rs   )r   �data_points�
date_range�basic_backtest�optimization_results�chartr}   )
r%   r2   rR   rb   r�   r�   r.   r*   r   �append)r   r   r   r   r�   r/   rN   �
basic_resultsr�   r�   �analysis_resultsr   r   r   �run_comprehensive_analysisy  s(   

�
z-TradingApplication.run_comprehensive_analysisra   c                 C   sF  t dd� �� t d|d � �� t d� � t d� t d|d � �� t d|d	 � �� |d
 }t d� t d|d
 � �� t d|d � d�� t d|d d�d|d d�d�� t d|d d�d�� t d|d � �� t d|d � �� |d r�t d� t|d dd � �D ]\}}t d!|d" � d#|jd�d$|j� �� q�t d%� dS )&z$Print comprehensive analysis summary�
z<============================================================u(   📊 COMPREHENSIVE ANALYSIS SUMMARY FOR r   u   📈 Data Overview:u     • Data Points: r�   u     • Date Range: r�   r�   u   
💰 Basic Backtest Results:u     • Total Trades: rr   u     • Success Rate: rw   �%u     • Total P&L: $�	total_pnlz.2fz (rt   z%)u     • Max Drawdown: �max_drawdownu     • Sharpe Ratio: rv   u     • Profit Factor: �
profit_factorr�   u-   
🎯 Parameter Optimization Results (Top 3):Nr�   z  r�   z	. Score: z% | Params: u%   
✅ Analysis completed successfully!)r%   �	enumerate�score�
parameters)r   ra   �basic�i�
opt_resultr   r   r   �print_analysis_summary�  s&   
"&z)TradingApplication.print_analysis_summary)r   r   Nr   )rc   rd   )r   r   T)�__name__�
__module__�__qualname__�__doc__r   �strri   �listr+   �	DataFramer2   r�   rR   r   rb   r�   r�   �Figurer�   �boolr�   r�   r   r   r   r   r      sL    ���
�$��
�\��
� ,���
�'r   �__main__�BTCUSDTr   ��   u*   🚀 Starting Enhanced Trading ApplicationzSymbol: z, Interval: z	, Limit: T)r   r   r   r�   u   
📊 Chart created with r}   z indicatorsu&   🎯 Analysis saved to results historyu   ❌ Analysis failed),r�   �pandasr+   �numpy�npr   r   �plotly.graph_objects�
graph_objectsr�   �plotly.subplotsr   �os�warnings�filterwarnings�enhanced_data_fetcherr   �enhanced_backtesterr   r   �parameter_optimizerr	   r
   �enhanced_indicatorsr   r   �custom_indicatorr
   �
chart_plotterr   �profitable_signal_generatorr   r   r�   �app�SYMBOL�INTERVAL�
DATA_LIMITr%   r�   ra   r�   r.   r(   r   r   r   r   �<module>   sN    
   %�
�