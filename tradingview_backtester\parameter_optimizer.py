"""
Advanced Parameter Optimization Engine
Includes grid search, random search, genetic algorithms, and walk-forward analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Callable, Any, Optional
from dataclasses import dataclass
import itertools
import random
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

@dataclass
class ParameterRange:
    """Define parameter optimization range"""
    name: str
    min_val: float
    max_val: float
    step: float = None
    values: List = None
    param_type: str = "float"  # 'float', 'int', 'choice'

@dataclass
class OptimizationResult:
    """Result of parameter optimization"""
    parameters: Dict
    metrics: Dict
    score: float
    rank: int = None

class ParameterOptimizer:
    """
    Advanced parameter optimization engine
    """
    
    def __init__(self, objective_function: Callable, scoring_metric: str = 'total_pnl_percentage'):
        """
        Initialize optimizer
        
        Args:
            objective_function: Function that takes parameters and returns metrics dict
            scoring_metric: Metric to optimize for
        """
        self.objective_function = objective_function
        self.scoring_metric = scoring_metric
        self.results = []
        
    def grid_search(self, parameter_ranges: List[ParameterRange], max_combinations: int = 1000) -> List[OptimizationResult]:
        """
        Perform grid search optimization
        """
        print(f"🔍 Starting grid search optimization...")
        
        # Generate all parameter combinations
        param_combinations = self._generate_grid_combinations(parameter_ranges)
        
        # Limit combinations if too many
        if len(param_combinations) > max_combinations:
            print(f"⚠️  Too many combinations ({len(param_combinations)}), sampling {max_combinations}")
            param_combinations = random.sample(param_combinations, max_combinations)
        
        print(f"📊 Testing {len(param_combinations)} parameter combinations...")
        
        results = []
        for i, params in enumerate(param_combinations):
            try:
                metrics = self.objective_function(params)
                score = metrics.get(self.scoring_metric, 0)
                
                result = OptimizationResult(
                    parameters=params.copy(),
                    metrics=metrics,
                    score=score
                )
                results.append(result)
                
                if (i + 1) % 50 == 0:
                    print(f"  Progress: {i + 1}/{len(param_combinations)} ({(i+1)/len(param_combinations)*100:.1f}%)")
                    
            except Exception as e:
                print(f"  ❌ Error with params {params}: {e}")
                continue
        
        # Sort by score
        results.sort(key=lambda x: x.score, reverse=True)
        for i, result in enumerate(results):
            result.rank = i + 1
        
        self.results = results
        print(f"✅ Grid search completed. Best score: {results[0].score:.3f}")
        return results
    
    def random_search(self, parameter_ranges: List[ParameterRange], n_iterations: int = 500) -> List[OptimizationResult]:
        """
        Perform random search optimization
        """
        print(f"🎲 Starting random search optimization ({n_iterations} iterations)...")
        
        results = []
        for i in range(n_iterations):
            try:
                # Generate random parameters
                params = self._generate_random_parameters(parameter_ranges)
                
                metrics = self.objective_function(params)
                score = metrics.get(self.scoring_metric, 0)
                
                result = OptimizationResult(
                    parameters=params,
                    metrics=metrics,
                    score=score
                )
                results.append(result)
                
                if (i + 1) % 100 == 0:
                    print(f"  Progress: {i + 1}/{n_iterations} ({(i+1)/n_iterations*100:.1f}%)")
                    
            except Exception as e:
                print(f"  ❌ Error at iteration {i}: {e}")
                continue
        
        # Sort by score
        results.sort(key=lambda x: x.score, reverse=True)
        for i, result in enumerate(results):
            result.rank = i + 1
        
        self.results = results
        print(f"✅ Random search completed. Best score: {results[0].score:.3f}")
        return results
    
    def genetic_algorithm(self, parameter_ranges: List[ParameterRange], 
                         population_size: int = 50, generations: int = 20,
                         mutation_rate: float = 0.1, crossover_rate: float = 0.8) -> List[OptimizationResult]:
        """
        Perform genetic algorithm optimization
        """
        print(f"🧬 Starting genetic algorithm optimization...")
        print(f"  Population: {population_size}, Generations: {generations}")
        
        # Initialize population
        population = []
        for _ in range(population_size):
            params = self._generate_random_parameters(parameter_ranges)
            population.append(params)
        
        best_scores = []
        
        for generation in range(generations):
            # Evaluate population
            fitness_scores = []
            for params in population:
                try:
                    metrics = self.objective_function(params)
                    score = metrics.get(self.scoring_metric, 0)
                    fitness_scores.append(score)
                except:
                    fitness_scores.append(-float('inf'))
            
            # Track best score
            best_score = max(fitness_scores)
            best_scores.append(best_score)
            
            print(f"  Generation {generation + 1}: Best score = {best_score:.3f}")
            
            # Selection (tournament selection)
            new_population = []
            
            # Keep best individuals (elitism)
            elite_count = max(1, population_size // 10)
            elite_indices = np.argsort(fitness_scores)[-elite_count:]
            for idx in elite_indices:
                new_population.append(population[idx].copy())
            
            # Generate rest of population
            while len(new_population) < population_size:
                if random.random() < crossover_rate:
                    # Crossover
                    parent1 = self._tournament_selection(population, fitness_scores)
                    parent2 = self._tournament_selection(population, fitness_scores)
                    child = self._crossover(parent1, parent2, parameter_ranges)
                else:
                    # Just select one parent
                    child = self._tournament_selection(population, fitness_scores)
                
                # Mutation
                if random.random() < mutation_rate:
                    child = self._mutate(child, parameter_ranges)
                
                new_population.append(child)
            
            population = new_population
        
        # Final evaluation
        final_results = []
        for params in population:
            try:
                metrics = self.objective_function(params)
                score = metrics.get(self.scoring_metric, 0)
                
                result = OptimizationResult(
                    parameters=params,
                    metrics=metrics,
                    score=score
                )
                final_results.append(result)
            except:
                continue
        
        # Sort by score
        final_results.sort(key=lambda x: x.score, reverse=True)
        for i, result in enumerate(final_results):
            result.rank = i + 1
        
        self.results = final_results
        print(f"✅ Genetic algorithm completed. Best score: {final_results[0].score:.3f}")
        return final_results
    
    def walk_forward_analysis(self, df: pd.DataFrame, parameter_ranges: List[ParameterRange],
                            train_periods: int = 252, test_periods: int = 63,
                            step_size: int = 21) -> Dict:
        """
        Perform walk-forward analysis
        """
        print(f"📈 Starting walk-forward analysis...")
        print(f"  Train: {train_periods} periods, Test: {test_periods} periods, Step: {step_size}")
        
        results = []
        start_idx = 0
        
        while start_idx + train_periods + test_periods <= len(df):
            # Define train and test periods
            train_end = start_idx + train_periods
            test_end = train_end + test_periods
            
            train_data = df.iloc[start_idx:train_end]
            test_data = df.iloc[train_end:test_end]
            
            print(f"  Period {len(results) + 1}: Train {train_data.index[0]} to {train_data.index[-1]}")
            print(f"                    Test {test_data.index[0]} to {test_data.index[-1]}")
            
            # Optimize on training data
            def train_objective(params):
                return self.objective_function(params, train_data)
            
            temp_optimizer = ParameterOptimizer(train_objective, self.scoring_metric)
            train_results = temp_optimizer.random_search(parameter_ranges, n_iterations=100)
            
            if not train_results:
                start_idx += step_size
                continue
            
            # Test best parameters on test data
            best_params = train_results[0].parameters
            test_metrics = self.objective_function(best_params, test_data)
            
            results.append({
                'period': len(results) + 1,
                'train_start': train_data.index[0],
                'train_end': train_data.index[-1],
                'test_start': test_data.index[0],
                'test_end': test_data.index[-1],
                'best_params': best_params,
                'train_score': train_results[0].score,
                'test_score': test_metrics.get(self.scoring_metric, 0),
                'test_metrics': test_metrics
            })
            
            start_idx += step_size
        
        # Calculate summary statistics
        if results:
            test_scores = [r['test_score'] for r in results]
            summary = {
                'periods': len(results),
                'avg_test_score': np.mean(test_scores),
                'std_test_score': np.std(test_scores),
                'min_test_score': np.min(test_scores),
                'max_test_score': np.max(test_scores),
                'positive_periods': sum(1 for score in test_scores if score > 0),
                'results': results
            }
            
            print(f"✅ Walk-forward analysis completed:")
            print(f"  Periods: {summary['periods']}")
            print(f"  Avg test score: {summary['avg_test_score']:.3f}")
            print(f"  Positive periods: {summary['positive_periods']}/{summary['periods']}")
            
            return summary
        
        return {'periods': 0, 'results': []}
    
    def _generate_grid_combinations(self, parameter_ranges: List[ParameterRange]) -> List[Dict]:
        """Generate all combinations for grid search"""
        param_lists = []
        param_names = []
        
        for param_range in parameter_ranges:
            param_names.append(param_range.name)
            
            if param_range.values is not None:
                param_lists.append(param_range.values)
            elif param_range.param_type == "choice":
                param_lists.append([param_range.min_val, param_range.max_val])
            else:
                if param_range.step is None:
                    param_range.step = (param_range.max_val - param_range.min_val) / 10
                
                if param_range.param_type == "int":
                    values = list(range(int(param_range.min_val), int(param_range.max_val) + 1, int(param_range.step)))
                else:
                    values = np.arange(param_range.min_val, param_range.max_val + param_range.step, param_range.step)
                
                param_lists.append(values)
        
        # Generate all combinations
        combinations = []
        for combo in itertools.product(*param_lists):
            param_dict = dict(zip(param_names, combo))
            combinations.append(param_dict)
        
        return combinations
    
    def _generate_random_parameters(self, parameter_ranges: List[ParameterRange]) -> Dict:
        """Generate random parameters within ranges"""
        params = {}
        
        for param_range in parameter_ranges:
            if param_range.values is not None:
                params[param_range.name] = random.choice(param_range.values)
            elif param_range.param_type == "choice":
                params[param_range.name] = random.choice([param_range.min_val, param_range.max_val])
            elif param_range.param_type == "int":
                params[param_range.name] = random.randint(int(param_range.min_val), int(param_range.max_val))
            else:
                params[param_range.name] = random.uniform(param_range.min_val, param_range.max_val)
        
        return params
    
    def _tournament_selection(self, population: List[Dict], fitness_scores: List[float], 
                            tournament_size: int = 3) -> Dict:
        """Tournament selection for genetic algorithm"""
        tournament_indices = random.sample(range(len(population)), min(tournament_size, len(population)))
        tournament_scores = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_scores)]
        return population[winner_idx].copy()
    
    def _crossover(self, parent1: Dict, parent2: Dict, parameter_ranges: List[ParameterRange]) -> Dict:
        """Crossover operation for genetic algorithm"""
        child = {}
        
        for param_range in parameter_ranges:
            name = param_range.name
            if random.random() < 0.5:
                child[name] = parent1[name]
            else:
                child[name] = parent2[name]
        
        return child
    
    def _mutate(self, individual: Dict, parameter_ranges: List[ParameterRange]) -> Dict:
        """Mutation operation for genetic algorithm"""
        mutated = individual.copy()
        
        # Mutate one random parameter
        param_range = random.choice(parameter_ranges)
        name = param_range.name
        
        if param_range.values is not None:
            mutated[name] = random.choice(param_range.values)
        elif param_range.param_type == "choice":
            mutated[name] = random.choice([param_range.min_val, param_range.max_val])
        elif param_range.param_type == "int":
            mutated[name] = random.randint(int(param_range.min_val), int(param_range.max_val))
        else:
            # Add some noise to current value
            current_val = mutated[name]
            noise = random.gauss(0, (param_range.max_val - param_range.min_val) * 0.1)
            new_val = current_val + noise
            new_val = max(param_range.min_val, min(param_range.max_val, new_val))
            mutated[name] = new_val
        
        return mutated
    
    def get_best_parameters(self, top_n: int = 1) -> List[OptimizationResult]:
        """Get top N parameter sets"""
        return self.results[:top_n] if self.results else []
    
    def analyze_parameter_sensitivity(self, parameter_name: str) -> Dict:
        """Analyze sensitivity of a specific parameter"""
        if not self.results:
            return {}
        
        param_values = []
        scores = []
        
        for result in self.results:
            if parameter_name in result.parameters:
                param_values.append(result.parameters[parameter_name])
                scores.append(result.score)
        
        if not param_values:
            return {}
        
        # Calculate correlation
        correlation = np.corrcoef(param_values, scores)[0, 1] if len(param_values) > 1 else 0
        
        return {
            'parameter': parameter_name,
            'correlation': correlation,
            'min_value': min(param_values),
            'max_value': max(param_values),
            'best_value': self.results[0].parameters.get(parameter_name),
            'value_score_pairs': list(zip(param_values, scores))
        }

if __name__ == '__main__':
    # Test the parameter optimizer
    print("=== Testing Parameter Optimizer ===")
    
    # Mock objective function
    def mock_objective(params, data=None):
        # Simulate some optimization function
        score = (
            params.get('rsi_length', 14) * 0.1 +
            params.get('ma_length', 20) * 0.05 +
            random.random() * 10  # Add some noise
        )
        return {
            'total_pnl_percentage': score,
            'success_rate': random.uniform(40, 80),
            'total_trades': random.randint(10, 50)
        }
    
    # Define parameter ranges
    param_ranges = [
        ParameterRange('rsi_length', 10, 30, step=2, param_type='int'),
        ParameterRange('ma_length', 10, 50, step=5, param_type='int'),
        ParameterRange('threshold', 0.1, 1.0, step=0.1, param_type='float')
    ]
    
    # Test grid search
    optimizer = ParameterOptimizer(mock_objective, 'total_pnl_percentage')
    results = optimizer.grid_search(param_ranges, max_combinations=100)
    
    print(f"\n📊 Top 3 Results:")
    for i, result in enumerate(results[:3]):
        print(f"  {i+1}. Score: {result.score:.3f}, Params: {result.parameters}")
    
    # Test parameter sensitivity
    sensitivity = optimizer.analyze_parameter_sensitivity('rsi_length')
    print(f"\n🔍 RSI Length Sensitivity:")
    print(f"  Correlation: {sensitivity.get('correlation', 0):.3f}")
    print(f"  Best value: {sensitivity.get('best_value', 'N/A')}")
