"""
Enhanced Trading Application Main Entry Point
Integrates all enhanced features for comprehensive trading analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import warnings
warnings.filterwarnings('ignore')

# Import enhanced modules
from enhanced_data_fetcher import EnhancedDataFetcher
from enhanced_backtester import EnhancedBacktester, BacktestConfig
from parameter_optimizer import ParameterOptimizer, ParameterRange
from enhanced_indicators import EnhancedIndicators, MultiTimeframeAnalysis
from custom_indicator import calculate_custom_indicator
from chart_plotter import plot_candlestick_chart_plotly
from profitable_signal_generator import apply_profitable_signals

class TradingApplication:
    """
    Enhanced Trading Application with comprehensive features
    """
    
    def __init__(self):
        self.data_fetcher = EnhancedDataFetcher()
        self.indicators = EnhancedIndicators()
        self.mtf_analysis = MultiTimeframeAnalysis()
        self.results_history = []
        
    def fetch_and_prepare_data(self, symbol: str, interval: str = "1d", 
                             limit: int = 500, sources: list = None) -> pd.DataFrame:
        """Fetch and prepare data with validation"""
        print(f"🔄 Fetching data for {symbol}...")
        
        # Fetch data with fallback
        df = self.data_fetcher.fetch_data_with_fallback(symbol, interval, limit, sources)
        
        if df is None:
            print("❌ Failed to fetch data")
            return None
        
        # Validate data
        is_valid, issues = self.data_fetcher.validate_data(df)
        if not is_valid:
            print(f"⚠️  Data validation issues: {issues}")
        
        # Set timestamp as index if not already
        if 'Timestamp' in df.columns and not isinstance(df.index, pd.DatetimeIndex):
            df.set_index('Timestamp', inplace=True)
        
        print(f"✅ Data prepared: {len(df)} records from {df.index[0]} to {df.index[-1]}")
        return df
    
    def calculate_enhanced_indicators(self, df: pd.DataFrame, config: dict = None) -> pd.DataFrame:
        """Calculate all enhanced indicators"""
        print("🔧 Calculating enhanced indicators...")

        # Calculate enhanced indicators first
        enhanced_df = self.indicators.calculate_all_indicators(df, config)

        # Calculate custom indicators (original) - this provides pt_trend, macd, rsi etc.
        if config and 'custom_indicator_params' in config:
            custom_df, signals = calculate_custom_indicator(enhanced_df, **config['custom_indicator_params'])
            enhanced_df = custom_df
        else:
            # Use default custom indicator parameters if none provided
            default_params = {
                'rsi_length': 14,
                'pt_ma_length': 20,
                'macd_fast_length': 12,
                'macd_slow_length': 26,
                'macd_signal_length': 9,
                'pt_ma_type': 'ema',
                'pt_atr_length': 14,
                'pt_bands_dist': 2.0,
                'nwe_h': 8.0,
                'nwe_mult': 3.0,
                'nwe_window_size': 50,
                'dmi_length': 14,
                'useDmi': True
            }
            custom_df, signals = calculate_custom_indicator(enhanced_df, **default_params)
            enhanced_df = custom_df

        # Apply profitable signal generation strategy
        enhanced_df = apply_profitable_signals(enhanced_df)

        return enhanced_df
    
    def run_enhanced_backtest(self, df: pd.DataFrame, config: BacktestConfig = None) -> dict:
        """Run enhanced backtest with advanced features"""
        print("🚀 Running enhanced backtest...")

        if config is None:
            config = BacktestConfig(
                initial_capital=10000,
                commission_rate=0.001,
                slippage_rate=0.0005,
                position_size_method="fixed_percentage",
                position_size_value=15.0,  # Use 15% of capital per trade
                stop_loss_percentage=0.04,  # 4% stop loss
                take_profit_percentage=0.08  # 8% take profit
            )
        
        backtester = EnhancedBacktester(config)
        results = backtester.run_backtest(df)
        
        return results
    
    def optimize_parameters(self, df: pd.DataFrame, method: str = "random", 
                          iterations: int = 100) -> list:
        """Optimize parameters using various methods"""
        print(f"🎯 Starting parameter optimization using {method} method...")
        
        # Define parameter ranges for optimization
        parameter_ranges = [
            ParameterRange('rsi_length', 10, 30, param_type='int'),
            ParameterRange('pt_ma_length', 10, 50, param_type='int'),
            ParameterRange('macd_fast_length', 8, 16, param_type='int'),
            ParameterRange('macd_slow_length', 20, 30, param_type='int'),
            ParameterRange('bb_length', 15, 25, param_type='int'),
            ParameterRange('bb_std', 1.5, 2.5, step=0.1),
        ]
        
        # Define objective function
        def objective_function(params, data=None):
            if data is None:
                data = df
            
            try:
                # Update indicator parameters
                indicator_config = {
                    'custom_indicator_params': {
                        'rsi_length': int(params.get('rsi_length', 14)),
                        'pt_ma_length': int(params.get('pt_ma_length', 20)),
                        'macd_fast_length': int(params.get('macd_fast_length', 12)),
                        'macd_slow_length': int(params.get('macd_slow_length', 26)),
                        'macd_signal_length': 9,
                        'pt_ma_type': 'ema',
                        'pt_atr_length': 14,
                        'pt_bands_dist': 2.0,
                        'nwe_h': 8.0,
                        'nwe_mult': 3.0,
                        'nwe_window_size': 50,
                        'dmi_length': 14,
                        'useDmi': True
                    },
                    'bb_length': int(params.get('bb_length', 20)),
                    'bb_std': params.get('bb_std', 2.0)
                }
                
                # Calculate indicators
                enhanced_data = self.calculate_enhanced_indicators(data, indicator_config)
                
                # Run backtest with improved configuration
                backtest_config = BacktestConfig(
                    initial_capital=10000,
                    commission_rate=0.001,
                    slippage_rate=0.0005,
                    position_size_method="fixed_percentage",
                    position_size_value=20.0,  # Use 20% of capital per trade
                    stop_loss_percentage=0.03,  # 3% stop loss
                    take_profit_percentage=0.06  # 6% take profit (2:1 ratio)
                )

                results = self.run_enhanced_backtest(enhanced_data, backtest_config)

                # Penalize strategies with too few trades
                if results['total_trades'] < 5:
                    results['total_pnl_percentage'] *= 0.5  # Reduce score for low activity

                # Bonus for good risk-adjusted returns
                if results['sharpe_ratio'] > 1.0:
                    results['total_pnl_percentage'] *= 1.2  # Bonus for good Sharpe ratio

                return results
                
            except Exception as e:
                print(f"Error in objective function: {e}")
                return {
                    'total_pnl_percentage': -100,
                    'success_rate': 0,
                    'total_trades': 0,
                    'sharpe_ratio': 0
                }
        
        # Run optimization
        optimizer = ParameterOptimizer(objective_function, 'total_pnl_percentage')
        
        if method == "grid":
            results = optimizer.grid_search(parameter_ranges, max_combinations=iterations)
        elif method == "genetic":
            results = optimizer.genetic_algorithm(parameter_ranges, 
                                                population_size=min(50, iterations//4),
                                                generations=max(10, iterations//20))
        else:  # random search
            results = optimizer.random_search(parameter_ranges, iterations)
        
        return results
    
    def create_enhanced_chart(self, df: pd.DataFrame, symbol: str, 
                            show_indicators: list = None) -> go.Figure:
        """Create enhanced chart with multiple indicators"""
        print("📊 Creating enhanced chart...")
        
        if show_indicators is None:
            show_indicators = ['bb_upper', 'bb_lower', 'supertrend', 'vwap']
        
        # Create subplots
        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.02,
            subplot_titles=[f'{symbol} Price & Indicators', 'Volume', 'RSI & Stochastic', 'MACD'],
            row_heights=[0.5, 0.15, 0.2, 0.15]
        )
        
        # Main price chart with candlesticks
        if 'bar_plot_color' in df.columns:
            # Use colored candlesticks based on trend
            for color, name in [('rgba(0,180,0,0.7)', 'Bullish'), ('rgba(180,0,0,0.7)', 'Bearish')]:
                mask = df['bar_plot_color'] == color
                if mask.any():
                    fig.add_trace(
                        go.Candlestick(
                            x=df[mask].index,
                            open=df[mask]['Open'],
                            high=df[mask]['High'],
                            low=df[mask]['Low'],
                            close=df[mask]['Close'],
                            name=name,
                            increasing_line_color=color,
                            decreasing_line_color=color,
                            increasing_fillcolor=color,
                            decreasing_fillcolor=color
                        ),
                        row=1, col=1
                    )
        else:
            # Standard candlesticks
            fig.add_trace(
                go.Candlestick(
                    x=df.index,
                    open=df['Open'],
                    high=df['High'],
                    low=df['Low'],
                    close=df['Close'],
                    name='Price'
                ),
                row=1, col=1
            )
        
        # Add indicators to price chart
        for indicator in show_indicators:
            if indicator in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=df[indicator],
                        mode='lines',
                        name=indicator.upper(),
                        line=dict(width=1)
                    ),
                    row=1, col=1
                )
        
        # Volume
        if 'Volume' in df.columns:
            fig.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['Volume'],
                    name='Volume',
                    marker_color='rgba(150,150,150,0.4)'
                ),
                row=2, col=1
            )
        
        # RSI and Stochastic
        if 'rsi' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['rsi'],
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple')
                ),
                row=3, col=1
            )
        
        if 'stoch_k' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['stoch_k'],
                    mode='lines',
                    name='Stoch %K',
                    line=dict(color='orange')
                ),
                row=3, col=1
            )
        
        # MACD
        if 'macd_line' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['macd_line'],
                    mode='lines',
                    name='MACD',
                    line=dict(color='blue')
                ),
                row=4, col=1
            )
        
        if 'macd_signal_line' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['macd_signal_line'],
                    mode='lines',
                    name='MACD Signal',
                    line=dict(color='red')
                ),
                row=4, col=1
            )
        
        # Add buy/sell signals
        if 'signal' in df.columns:
            buy_signals = df[df['signal'] == 1]
            sell_signals = df[df['signal'] == -1]
            
            if not buy_signals.empty:
                fig.add_trace(
                    go.Scatter(
                        x=buy_signals.index,
                        y=buy_signals['Low'] * 0.98,
                        mode='markers',
                        name='Buy Signal',
                        marker=dict(symbol='triangle-up', size=10, color='green')
                    ),
                    row=1, col=1
                )
            
            if not sell_signals.empty:
                fig.add_trace(
                    go.Scatter(
                        x=sell_signals.index,
                        y=sell_signals['High'] * 1.02,
                        mode='markers',
                        name='Sell Signal',
                        marker=dict(symbol='triangle-down', size=10, color='red')
                    ),
                    row=1, col=1
                )
        
        # Update layout
        fig.update_layout(
            title=f'{symbol} Enhanced Trading Analysis',
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True
        )
        
        # Add horizontal lines for RSI
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
        
        return fig
    
    def run_comprehensive_analysis(self, symbol: str, interval: str = "1d", 
                                 limit: int = 500, optimize: bool = True) -> dict:
        """Run comprehensive trading analysis"""
        print(f"🎯 Starting comprehensive analysis for {symbol}")
        
        # 1. Fetch data
        df = self.fetch_and_prepare_data(symbol, interval, limit)
        if df is None:
            return None
        
        # 2. Calculate indicators
        enhanced_df = self.calculate_enhanced_indicators(df)
        
        # 3. Run basic backtest
        basic_results = self.run_enhanced_backtest(enhanced_df)
        
        # 4. Optimize parameters if requested
        optimization_results = None
        if optimize:
            optimization_results = self.optimize_parameters(enhanced_df, method="random", iterations=50)
        
        # 5. Create chart
        chart = self.create_enhanced_chart(enhanced_df, symbol)
        
        # 6. Compile results
        analysis_results = {
            'symbol': symbol,
            'data_points': len(enhanced_df),
            'date_range': f"{enhanced_df.index[0]} to {enhanced_df.index[-1]}",
            'basic_backtest': basic_results,
            'optimization_results': optimization_results[:5] if optimization_results else None,
            'chart': chart,
            'enhanced_data': enhanced_df
        }
        
        self.results_history.append(analysis_results)
        
        return analysis_results
    
    def print_analysis_summary(self, results: dict):
        """Print comprehensive analysis summary"""
        print(f"\n{'='*60}")
        print(f"📊 COMPREHENSIVE ANALYSIS SUMMARY FOR {results['symbol']}")
        print(f"{'='*60}")
        
        print(f"📈 Data Overview:")
        print(f"  • Data Points: {results['data_points']}")
        print(f"  • Date Range: {results['date_range']}")
        
        basic = results['basic_backtest']
        print(f"\n💰 Basic Backtest Results:")
        print(f"  • Total Trades: {basic['total_trades']}")
        print(f"  • Success Rate: {basic['success_rate']}%")
        print(f"  • Total P&L: ${basic['total_pnl']:.2f} ({basic['total_pnl_percentage']:.2f}%)")
        print(f"  • Max Drawdown: {basic['max_drawdown']:.2f}%")
        print(f"  • Sharpe Ratio: {basic['sharpe_ratio']}")
        print(f"  • Profit Factor: {basic['profit_factor']}")
        
        if results['optimization_results']:
            print(f"\n🎯 Parameter Optimization Results (Top 3):")
            for i, opt_result in enumerate(results['optimization_results'][:3]):
                print(f"  {i+1}. Score: {opt_result.score:.2f}% | Params: {opt_result.parameters}")
        
        print(f"\n✅ Analysis completed successfully!")

if __name__ == '__main__':
    # Run enhanced trading application
    app = TradingApplication()
    
    # Configuration
    SYMBOL = "BTCUSDT"
    INTERVAL = "1d"
    DATA_LIMIT = 200
    
    print("🚀 Starting Enhanced Trading Application")
    print(f"Symbol: {SYMBOL}, Interval: {INTERVAL}, Limit: {DATA_LIMIT}")
    
    # Run comprehensive analysis
    results = app.run_comprehensive_analysis(
        symbol=SYMBOL,
        interval=INTERVAL,
        limit=DATA_LIMIT,
        optimize=True
    )
    
    if results:
        # Print summary
        app.print_analysis_summary(results)
        
        # Save chart (optional)
        # results['chart'].write_html(f"enhanced_analysis_{SYMBOL}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
        
        print(f"\n📊 Chart created with {len(results['enhanced_data'].columns)} indicators")
        print(f"🎯 Analysis saved to results history")
    else:
        print("❌ Analysis failed")
