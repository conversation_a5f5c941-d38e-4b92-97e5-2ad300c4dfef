"""
Enhanced Data Fetcher with Multiple Sources
Supports Binance, Yahoo Finance, CoinGecko, and Alpha Vantage APIs
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Optional, Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Optional imports
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    print("yfinance not available - Yahoo Finance data source disabled")

class EnhancedDataFetcher:
    """
    Enhanced data fetcher with multiple data sources and fallback mechanisms
    """
    
    def __init__(self):
        self.sources = ['binance', 'yahoo', 'coingecko']
        self.cache = {}
        
    def fetch_binance_klines(self, symbol: str, interval: str, start_time_ms: int = None, 
                           end_time_ms: int = None, limit: int = 500) -> Optional[pd.DataFrame]:
        """
        Fetches kline/candlestick data from Binance API.
        """
        base_url = "https://api.binance.com/api/v3/klines"
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }

        if start_time_ms:
            params["startTime"] = start_time_ms
        if end_time_ms:
            params["endTime"] = end_time_ms

        try:
            response = requests.get(base_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            df = pd.DataFrame(data, columns=[
                'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume',
                'CloseTime', 'QuoteAssetVolume', 'NumberOfTrades',
                'TakerBuyBaseAssetVolume', 'TakerBuyQuoteAssetVolume', 'Ignore'
            ])

            df = df[['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']]
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
            
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col])

            df.sort_values(by='Timestamp', inplace=True)
            df.reset_index(drop=True, inplace=True)
            
            print(f"✓ Binance: Fetched {len(df)} records for {symbol}")
            return df

        except Exception as e:
            print(f"✗ Binance API Error: {e}")
            return None

    def fetch_yahoo_finance(self, symbol: str, period: str = "1y", interval: str = "1d") -> Optional[pd.DataFrame]:
        """
        Fetches data from Yahoo Finance using yfinance
        """
        if not YFINANCE_AVAILABLE:
            print("✗ Yahoo Finance: yfinance not available")
            return None

        try:
            # Convert crypto symbols for Yahoo Finance
            if symbol.endswith('USDT'):
                yahoo_symbol = symbol.replace('USDT', '-USD')
            else:
                yahoo_symbol = symbol

            ticker = yf.Ticker(yahoo_symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                print(f"✗ Yahoo Finance: No data for {yahoo_symbol}")
                return None
                
            # Rename columns to match our format
            data = data.rename(columns={
                'Open': 'Open',
                'High': 'High', 
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })
            
            # Reset index to get Timestamp as column
            data.reset_index(inplace=True)
            data.rename(columns={'Date': 'Timestamp'}, inplace=True)
            
            print(f"✓ Yahoo Finance: Fetched {len(data)} records for {yahoo_symbol}")
            return data
            
        except Exception as e:
            print(f"✗ Yahoo Finance Error: {e}")
            return None

    def fetch_coingecko(self, symbol: str, days: int = 365) -> Optional[pd.DataFrame]:
        """
        Fetches data from CoinGecko API
        """
        try:
            # Convert symbol to CoinGecko format
            coin_map = {
                'BTCUSDT': 'bitcoin',
                'ETHUSDT': 'ethereum',
                'ADAUSDT': 'cardano',
                'DOTUSDT': 'polkadot',
                'LINKUSDT': 'chainlink'
            }
            
            coin_id = coin_map.get(symbol, symbol.lower().replace('usdt', ''))
            
            url = f"https://api.coingecko.com/api/v3/coins/{coin_id}/ohlc"
            params = {
                'vs_currency': 'usd',
                'days': days
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if not data:
                print(f"✗ CoinGecko: No data for {coin_id}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(data, columns=['Timestamp', 'Open', 'High', 'Low', 'Close'])
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
            df['Volume'] = 0  # CoinGecko OHLC doesn't include volume
            
            for col in ['Open', 'High', 'Low', 'Close']:
                df[col] = pd.to_numeric(df[col])
                
            df.sort_values(by='Timestamp', inplace=True)
            df.reset_index(drop=True, inplace=True)
            
            print(f"✓ CoinGecko: Fetched {len(df)} records for {coin_id}")
            return df
            
        except Exception as e:
            print(f"✗ CoinGecko Error: {e}")
            return None

    def fetch_data_with_fallback(self, symbol: str, interval: str = "1d", 
                               limit: int = 500, sources: List[str] = None) -> Optional[pd.DataFrame]:
        """
        Attempts to fetch data from multiple sources with fallback
        """
        if sources is None:
            sources = self.sources
            
        print(f"🔄 Fetching data for {symbol} with fallback mechanism...")
        
        for source in sources:
            try:
                if source == 'binance':
                    data = self.fetch_binance_klines(symbol, interval, limit=limit)
                elif source == 'yahoo':
                    # Convert interval for Yahoo Finance
                    yahoo_interval = self._convert_interval_for_yahoo(interval)
                    data = self.fetch_yahoo_finance(symbol, interval=yahoo_interval)
                elif source == 'coingecko':
                    data = self.fetch_coingecko(symbol, days=limit)
                else:
                    continue
                    
                if data is not None and not data.empty and len(data) >= limit * 0.5:
                    print(f"✅ Successfully fetched data from {source.upper()}")
                    return self._standardize_data(data)
                    
            except Exception as e:
                print(f"✗ Error with {source}: {e}")
                continue
                
        print("❌ All data sources failed, generating sample data...")
        return self._generate_sample_data(symbol, limit)

    def _convert_interval_for_yahoo(self, interval: str) -> str:
        """Convert Binance interval format to Yahoo Finance format"""
        interval_map = {
            '1m': '1m',
            '5m': '5m', 
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '4h': '4h',
            '1d': '1d',
            '1w': '1wk',
            '1M': '1mo'
        }
        return interval_map.get(interval, '1d')

    def _standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data format across all sources"""
        required_cols = ['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
        
        # Ensure all required columns exist
        for col in required_cols:
            if col not in df.columns:
                if col == 'Volume':
                    df[col] = 0
                else:
                    print(f"Warning: Missing column {col}")
                    return None
        
        # Ensure proper data types
        df['Timestamp'] = pd.to_datetime(df['Timestamp'])
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove any rows with NaN values in OHLC
        df = df.dropna(subset=['Open', 'High', 'Low', 'Close'])
        
        # Sort by timestamp
        df = df.sort_values('Timestamp').reset_index(drop=True)
        
        return df

    def _generate_sample_data(self, symbol: str, days: int = 200) -> pd.DataFrame:
        """Generate realistic sample data as fallback"""
        print(f"📊 Generating sample data for {symbol}...")
        
        # Base price depending on symbol
        base_prices = {
            'BTCUSDT': 45000,
            'ETHUSDT': 3000,
            'ADAUSDT': 0.5,
            'DOTUSDT': 25,
            'LINKUSDT': 15
        }
        
        base_price = base_prices.get(symbol, 100)
        
        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate realistic price movements
        np.random.seed(42)  # For reproducible results
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
        
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, base_price * 0.1))  # Prevent negative prices
        
        # Generate OHLC data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # Add some intraday volatility
            volatility = abs(np.random.normal(0, 0.01))
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            
            # Ensure OHLC relationships are valid
            if i == 0:
                open_price = price
            else:
                open_price = prices[i-1]
                
            close_price = price
            
            # Adjust high and low to be valid
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            volume = np.random.randint(1000, 10000)
            
            data.append({
                'Timestamp': date,
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close_price,
                'Volume': volume
            })
        
        df = pd.DataFrame(data)
        print(f"✅ Generated {len(df)} sample records for {symbol}")
        return df

    def validate_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate data quality and return issues found"""
        issues = []
        
        if df is None or df.empty:
            return False, ["DataFrame is None or empty"]
        
        # Check required columns
        required_cols = ['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing columns: {missing_cols}")
        
        # Check for NaN values
        nan_counts = df[['Open', 'High', 'Low', 'Close']].isnull().sum()
        if nan_counts.sum() > 0:
            issues.append(f"NaN values found: {nan_counts.to_dict()}")
        
        # Check OHLC relationships
        invalid_ohlc = (
            (df['High'] < df['Low']) |
            (df['High'] < df['Open']) |
            (df['High'] < df['Close']) |
            (df['Low'] > df['Open']) |
            (df['Low'] > df['Close'])
        ).sum()
        
        if invalid_ohlc > 0:
            issues.append(f"Invalid OHLC relationships in {invalid_ohlc} rows")
        
        # Check for duplicate timestamps
        duplicates = df['Timestamp'].duplicated().sum()
        if duplicates > 0:
            issues.append(f"Duplicate timestamps: {duplicates}")
        
        return len(issues) == 0, issues

# Convenience function for backward compatibility
def fetch_binance_klines(symbol: str, interval: str, start_time_ms: int = None, 
                        end_time_ms: int = None, limit: int = 500) -> Optional[pd.DataFrame]:
    """Backward compatibility function"""
    fetcher = EnhancedDataFetcher()
    return fetcher.fetch_binance_klines(symbol, interval, start_time_ms, end_time_ms, limit)

if __name__ == '__main__':
    # Test the enhanced data fetcher
    fetcher = EnhancedDataFetcher()
    
    print("=== Testing Enhanced Data Fetcher ===")
    
    # Test with fallback mechanism
    data = fetcher.fetch_data_with_fallback("BTCUSDT", "1d", limit=100)
    
    if data is not None:
        print(f"\n📊 Data Summary:")
        print(f"Records: {len(data)}")
        print(f"Date range: {data['Timestamp'].min()} to {data['Timestamp'].max()}")
        print(f"Price range: ${data['Close'].min():.2f} - ${data['Close'].max():.2f}")
        
        # Validate data
        is_valid, issues = fetcher.validate_data(data)
        print(f"\n✅ Data validation: {'PASSED' if is_valid else 'FAILED'}")
        if issues:
            for issue in issues:
                print(f"  ⚠️  {issue}")
        
        print("\nFirst 5 rows:")
        print(data.head())
    else:
        print("❌ Failed to fetch any data")
