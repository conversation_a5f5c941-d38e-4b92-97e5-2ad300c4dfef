"""
Enhanced Technical Indicators Library
Includes advanced indicators and multi-timeframe analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False

class EnhancedIndicators:
    """
    Enhanced technical indicators with advanced features
    """
    
    @staticmethod
    def bollinger_bands(series: pd.Series, length: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands"""
        sma = series.rolling(window=length).mean()
        std = series.rolling(window=length).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        # Calculate %B and bandwidth
        percent_b = (series - lower) / (upper - lower)
        bandwidth = (upper - lower) / sma
        
        return {
            'bb_upper': upper,
            'bb_middle': sma,
            'bb_lower': lower,
            'bb_percent_b': percent_b,
            'bb_bandwidth': bandwidth
        }
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """Calculate Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            'stoch_k': k_percent,
            'stoch_d': d_percent
        }
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Williams %R"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * (highest_high - close) / (highest_high - lowest_low)
        return williams_r
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """Calculate On-Balance Volume"""
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    @staticmethod
    def vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Calculate Volume Weighted Average Price"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def supertrend(high: pd.Series, low: pd.Series, close: pd.Series, 
                  period: int = 10, multiplier: float = 3.0) -> Dict[str, pd.Series]:
        """Calculate SuperTrend indicator"""
        atr = EnhancedIndicators.atr(high, low, close, period)
        hl2 = (high + low) / 2
        
        upper_band = hl2 + (multiplier * atr)
        lower_band = hl2 - (multiplier * atr)
        
        # Initialize arrays
        supertrend = pd.Series(index=close.index, dtype=float)
        direction = pd.Series(index=close.index, dtype=int)
        
        for i in range(len(close)):
            if i == 0:
                supertrend.iloc[i] = lower_band.iloc[i]
                direction.iloc[i] = 1
            else:
                # Calculate final upper and lower bands
                if upper_band.iloc[i] < upper_band.iloc[i-1] or close.iloc[i-1] > upper_band.iloc[i-1]:
                    final_upper = upper_band.iloc[i]
                else:
                    final_upper = upper_band.iloc[i-1]
                
                if lower_band.iloc[i] > lower_band.iloc[i-1] or close.iloc[i-1] < lower_band.iloc[i-1]:
                    final_lower = lower_band.iloc[i]
                else:
                    final_lower = lower_band.iloc[i-1]
                
                # Determine trend direction
                if close.iloc[i] <= final_lower:
                    direction.iloc[i] = -1
                    supertrend.iloc[i] = final_upper
                elif close.iloc[i] >= final_upper:
                    direction.iloc[i] = 1
                    supertrend.iloc[i] = final_lower
                else:
                    direction.iloc[i] = direction.iloc[i-1]
                    if direction.iloc[i] == 1:
                        supertrend.iloc[i] = final_lower
                    else:
                        supertrend.iloc[i] = final_upper
        
        return {
            'supertrend': supertrend,
            'supertrend_direction': direction
        }
    
    @staticmethod
    def ichimoku(high: pd.Series, low: pd.Series, close: pd.Series,
                tenkan_period: int = 9, kijun_period: int = 26, 
                senkou_b_period: int = 52) -> Dict[str, pd.Series]:
        """Calculate Ichimoku Cloud"""
        # Tenkan-sen (Conversion Line)
        tenkan_sen = (high.rolling(window=tenkan_period).max() + 
                     low.rolling(window=tenkan_period).min()) / 2
        
        # Kijun-sen (Base Line)
        kijun_sen = (high.rolling(window=kijun_period).max() + 
                    low.rolling(window=kijun_period).min()) / 2
        
        # Senkou Span A (Leading Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(kijun_period)
        
        # Senkou Span B (Leading Span B)
        senkou_span_b = ((high.rolling(window=senkou_b_period).max() + 
                         low.rolling(window=senkou_b_period).min()) / 2).shift(kijun_period)
        
        # Chikou Span (Lagging Span)
        chikou_span = close.shift(-kijun_period)
        
        return {
            'ichimoku_tenkan': tenkan_sen,
            'ichimoku_kijun': kijun_sen,
            'ichimoku_senkou_a': senkou_span_a,
            'ichimoku_senkou_b': senkou_span_b,
            'ichimoku_chikou': chikou_span
        }
    
    @staticmethod
    def pivot_points(high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, pd.Series]:
        """Calculate Pivot Points"""
        pivot = (high.shift(1) + low.shift(1) + close.shift(1)) / 3
        
        r1 = 2 * pivot - low.shift(1)
        s1 = 2 * pivot - high.shift(1)
        
        r2 = pivot + (high.shift(1) - low.shift(1))
        s2 = pivot - (high.shift(1) - low.shift(1))
        
        r3 = high.shift(1) + 2 * (pivot - low.shift(1))
        s3 = low.shift(1) - 2 * (high.shift(1) - pivot)
        
        return {
            'pivot': pivot,
            'r1': r1, 'r2': r2, 'r3': r3,
            's1': s1, 's2': s2, 's3': s3
        }
    
    @staticmethod
    def fibonacci_retracement(high: pd.Series, low: pd.Series, period: int = 20) -> Dict[str, pd.Series]:
        """Calculate Fibonacci Retracement levels"""
        rolling_high = high.rolling(window=period).max()
        rolling_low = low.rolling(window=period).min()
        
        diff = rolling_high - rolling_low
        
        fib_levels = {
            'fib_0': rolling_high,
            'fib_236': rolling_high - 0.236 * diff,
            'fib_382': rolling_high - 0.382 * diff,
            'fib_500': rolling_high - 0.500 * diff,
            'fib_618': rolling_high - 0.618 * diff,
            'fib_786': rolling_high - 0.786 * diff,
            'fib_100': rolling_low
        }
        
        return fib_levels
    
    @staticmethod
    def money_flow_index(high: pd.Series, low: pd.Series, close: pd.Series, 
                        volume: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Money Flow Index"""
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        positive_flow = pd.Series(index=close.index, dtype=float)
        negative_flow = pd.Series(index=close.index, dtype=float)
        
        for i in range(1, len(typical_price)):
            if typical_price.iloc[i] > typical_price.iloc[i-1]:
                positive_flow.iloc[i] = money_flow.iloc[i]
                negative_flow.iloc[i] = 0
            elif typical_price.iloc[i] < typical_price.iloc[i-1]:
                positive_flow.iloc[i] = 0
                negative_flow.iloc[i] = money_flow.iloc[i]
            else:
                positive_flow.iloc[i] = 0
                negative_flow.iloc[i] = 0
        
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()
        
        money_ratio = positive_mf / negative_mf
        mfi = 100 - (100 / (1 + money_ratio))
        
        return mfi
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame, config: Dict = None) -> pd.DataFrame:
        """Calculate all indicators for a DataFrame"""
        if config is None:
            config = {
                'bb_length': 20, 'bb_std': 2.0,
                'stoch_k': 14, 'stoch_d': 3,
                'williams_period': 14,
                'atr_period': 14,
                'supertrend_period': 10, 'supertrend_mult': 3.0,
                'mfi_period': 14,
                'pivot_period': 20,
                'fib_period': 20
            }
        
        result_df = df.copy()
        
        try:
            # Bollinger Bands
            bb = EnhancedIndicators.bollinger_bands(
                df['Close'], config.get('bb_length', 20), config.get('bb_std', 2.0)
            )
            for key, value in bb.items():
                result_df[key] = value
            
            # Stochastic
            stoch = EnhancedIndicators.stochastic(
                df['High'], df['Low'], df['Close'], 
                config.get('stoch_k', 14), config.get('stoch_d', 3)
            )
            for key, value in stoch.items():
                result_df[key] = value
            
            # Williams %R
            result_df['williams_r'] = EnhancedIndicators.williams_r(
                df['High'], df['Low'], df['Close'], config.get('williams_period', 14)
            )
            
            # OBV
            if 'Volume' in df.columns:
                result_df['obv'] = EnhancedIndicators.obv(df['Close'], df['Volume'])
                
                # VWAP
                result_df['vwap'] = EnhancedIndicators.vwap(
                    df['High'], df['Low'], df['Close'], df['Volume']
                )
                
                # Money Flow Index
                result_df['mfi'] = EnhancedIndicators.money_flow_index(
                    df['High'], df['Low'], df['Close'], df['Volume'], 
                    config.get('mfi_period', 14)
                )
            
            # ATR
            result_df['atr'] = EnhancedIndicators.atr(
                df['High'], df['Low'], df['Close'], config.get('atr_period', 14)
            )
            
            # SuperTrend
            supertrend = EnhancedIndicators.supertrend(
                df['High'], df['Low'], df['Close'],
                config.get('supertrend_period', 10), config.get('supertrend_mult', 3.0)
            )
            for key, value in supertrend.items():
                result_df[key] = value
            
            # Ichimoku
            ichimoku = EnhancedIndicators.ichimoku(df['High'], df['Low'], df['Close'])
            for key, value in ichimoku.items():
                result_df[key] = value
            
            # Pivot Points
            pivots = EnhancedIndicators.pivot_points(df['High'], df['Low'], df['Close'])
            for key, value in pivots.items():
                result_df[f'pivot_{key}'] = value
            
            # Fibonacci Retracement
            fib = EnhancedIndicators.fibonacci_retracement(
                df['High'], df['Low'], config.get('fib_period', 20)
            )
            for key, value in fib.items():
                result_df[key] = value
            
            print(f"✅ Calculated {len([col for col in result_df.columns if col not in df.columns])} additional indicators")
            
        except Exception as e:
            print(f"❌ Error calculating indicators: {e}")
        
        return result_df

class MultiTimeframeAnalysis:
    """
    Multi-timeframe analysis tools
    """
    
    @staticmethod
    def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample data to different timeframe"""
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'Timestamp' in df.columns:
                df = df.set_index('Timestamp')
            else:
                raise ValueError("DataFrame must have DatetimeIndex or Timestamp column")
        
        # Resample OHLCV data
        resampled = df.resample(timeframe).agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        return resampled
    
    @staticmethod
    def get_higher_timeframe_signal(df: pd.DataFrame, signal_column: str, 
                                  higher_tf: str = '4H') -> pd.Series:
        """Get signal from higher timeframe"""
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("DataFrame must have DatetimeIndex")
        
        # Resample to higher timeframe
        htf_data = MultiTimeframeAnalysis.resample_data(df, higher_tf)
        
        if signal_column in df.columns:
            htf_signal = df[signal_column].resample(higher_tf).last()
        else:
            htf_signal = pd.Series(0, index=htf_data.index)
        
        # Forward fill to original timeframe
        htf_signal_filled = htf_signal.reindex(df.index, method='ffill')
        
        return htf_signal_filled
    
    @staticmethod
    def calculate_mtf_confluence(df: pd.DataFrame, timeframes: List[str], 
                               signal_column: str = 'signal') -> pd.Series:
        """Calculate multi-timeframe confluence"""
        confluence_score = pd.Series(0, index=df.index)
        
        for tf in timeframes:
            try:
                htf_signal = MultiTimeframeAnalysis.get_higher_timeframe_signal(df, signal_column, tf)
                confluence_score += htf_signal
            except Exception as e:
                print(f"Warning: Could not calculate {tf} timeframe signal: {e}")
        
        # Normalize to -1 to 1 range
        if len(timeframes) > 0:
            confluence_score = confluence_score / len(timeframes)
        
        return confluence_score

    @staticmethod
    def calculate_signal_confluence(df: pd.DataFrame) -> pd.Series:
        """Calculate signal confluence from multiple indicators"""
        signals = pd.Series(0, index=df.index)
        signal_count = 0

        # RSI signals
        if 'rsi' in df.columns:
            rsi_signal = np.where(df['rsi'] < 30, 1, np.where(df['rsi'] > 70, -1, 0))
            signals += rsi_signal
            signal_count += 1

        # Bollinger Bands signals
        if 'bb_percent_b' in df.columns:
            bb_signal = np.where(df['bb_percent_b'] < 0, 1, np.where(df['bb_percent_b'] > 1, -1, 0))
            signals += bb_signal
            signal_count += 1

        # Stochastic signals
        if 'stoch_k' in df.columns:
            stoch_signal = np.where(df['stoch_k'] < 20, 1, np.where(df['stoch_k'] > 80, -1, 0))
            signals += stoch_signal
            signal_count += 1

        # SuperTrend signals
        if 'supertrend_direction' in df.columns:
            signals += df['supertrend_direction']
            signal_count += 1

        # Normalize signals
        if signal_count > 0:
            signals = signals / signal_count

        return signals

if __name__ == '__main__':
    # Test enhanced indicators
    print("=== Testing Enhanced Indicators ===")

    # Create sample data
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)

    prices = [100]
    for _ in range(99):
        change = np.random.normal(0, 0.02)
        prices.append(prices[-1] * (1 + change))

    df = pd.DataFrame({
        'Timestamp': dates,
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': np.random.randint(1000, 5000, 100)
    })
    df.set_index('Timestamp', inplace=True)

    # Calculate all indicators
    enhanced_df = EnhancedIndicators.calculate_all_indicators(df)

    print(f"📊 Original columns: {len(df.columns)}")
    print(f"📊 Enhanced columns: {len(enhanced_df.columns)}")
    print(f"📊 New indicators: {len(enhanced_df.columns) - len(df.columns)}")

    # Show some indicator values
    print(f"\n📈 Sample indicator values (last row):")
    indicators_to_show = ['bb_upper', 'bb_lower', 'stoch_k', 'williams_r', 'supertrend']
    for indicator in indicators_to_show:
        if indicator in enhanced_df.columns:
            print(f"  {indicator}: {enhanced_df[indicator].iloc[-1]:.3f}")

    # Test signal confluence
    confluence = EnhancedIndicators.calculate_signal_confluence(enhanced_df)
    print(f"\n🎯 Signal confluence calculated: {len(confluence)} values")
    print(f"  Last confluence score: {confluence.iloc[-1]:.3f}")

    # Test multi-timeframe analysis
    print(f"\n🔄 Testing multi-timeframe analysis...")
    mtf = MultiTimeframeAnalysis()

    # Add a simple signal
    enhanced_df['signal'] = np.where(enhanced_df['Close'] > enhanced_df['bb_middle'], 1, -1)

    # Test higher timeframe signal
    try:
        htf_signal = mtf.get_higher_timeframe_signal(enhanced_df, 'signal', '7D')
        print(f"  Higher timeframe signal calculated: {len(htf_signal)} values")
    except Exception as e:
        print(f"  Error in HTF analysis: {e}")

    print("✅ Enhanced indicators test completed")
