o

    R%Qh=  �                
   @   s  d Z ddlZddlZddlmZmZ ddlZe�	d� G dd� d�Z
dejdejfd	d
�Ze
dk�r�ed� ejd
ddd�Zej�d� e�ej�ddd��Zde�e� Zejeedej�ddd�  edej�ddd�  eej�ddd�d�ed�Zed �d��� ed< ed �d��� ed< ed �� Ze�edkd��d ��� Z e �edk d��d ��� Z!e e! Z"ddde"   ed!< ed j#d"d#��� Z$ed j#d$d#��� Z%e$e% ed%< ed% j#d&d#��� ed'< ed% ed'  ed(< ed �d��� ed)< ed �d��&� Z'ed) e'd*  ed+< ed) e'd*  ed,< ee�Z(e(d- dk�)� Z*e(d- d.k�)� Z+ed/e*� d0e+� d1�� ed2e*e+ e,e� d d3�d4�� e
� Z-d5e-j.fd6e-j/fd7e-j0fd8e-j1fd9e-j2fgZ3e3D ]\Z4Z5e5e�Z6e6dk�)� Z7ee4� d:e7� d;�� �qeed<� dS dS )=z[
Profitable Signal Generation System
Focus on high-probability, profitable trading signals
�    N)�Dict�Tuple�ignorec                   @   s�   e Zd ZdZdd� Zdejdejfdd�Zdejdejfdd	�Z	dejdejfd
d�Z
dejdejfdd
�Zdejdejfdd�Zdejdejfdd�Z
dejdejdejfdd�Zdejdejfdd�ZdS )�ProfitableSignalGeneratorzE
    Generate profitable trading signals using proven strategies
    c                 C   s
   d| _ d S )N�   )�min_signal_gap)�self� r	   �L/mnt/persist/workspace/tradingview_backtester/profitable_signal_generator.py�__init__   s   
z"ProfitableSignalGenerator.__init__�df�returnc                 C   s�   t jd|jd�}d|jvr|d �d��� |d< d|jvr(|d �d��� |d< |d |d k|d �d�|d �d�k@ }|d |d k |d �d�|d �d�k@ }|d |d k}|d |d k }d|j||@ < d	|j||@ < |S )
zP
        Generate signals based on moving average golden cross strategy
        r   ��index�ma_20�Close�   �ma_50�2   �   �������pd�Seriesr   �columns�rolling�mean�shift�loc)r   r   �signals�golden_cross�death_cross�price_above_ma20�price_below_ma20r	   r	   r
   �generate_golden_cross_signals   s$   

����z7ProfitableSignalGenerator.generate_golden_cross_signalsc                 C   s�   t jd|jd�}d|jvr|S |d dk }|d |d �d�k}|d |d �d�k}|d dk}|d |d �d�k }|d |d �d�k }||@ |@ }	||@ |@ }
d|j|	< d|j|
< |S )	z[
        Generate signals based on RSI oversold/overbought with price confirmation
        r   r   �rsi�   r   r   �K   r   )r   r   r   r   r   r   )r   r   r   �rsi_oversold�
rsi_rising�price_rising�rsi_overbought�rsi_falling�
price_falling�buy_signals�sell_signalsr	   r	   r
   �generate_rsi_divergence_signals5   s   


z9ProfitableSignalGenerator.generate_rsi_divergence_signalsc           
         s�   t jd� jd�}g d�}t� fdd�|D ��s|S � d � d k� d �d�� d �d�k@ }� d � d k � d �d�� d �d�k@ }� d	 � d	 �d�k}� d	 � d	 �d�k }||@ }||@ }	d|j|< d
|j|	< |S )zU
        Generate signals based on MACD momentum with histogram confirmation
        r   r   )�	macd_line�macd_signal_line�	macd_histc                 3   �   � | ]}|� j v V  qd S �N�r   ��.0�col�r   r	   r
   �	<genexpr>Z   �   � zKProfitableSignalGenerator.generate_macd_momentum_signals.<locals>.<genexpr>r1   r2   r   r3   r   )r   r   r   �allr   r   )
r   r   r   �
required_cols�macd_bullish_cross�macd_bearish_cross�hist_increasing�hist_decreasingr.   r/   r	   r:   r
   �generate_macd_momentum_signalsS   s&   ����

z8ProfitableSignalGenerator.generate_macd_momentum_signalsc                    s�   t jd� jd�}g d�}t� fdd�|D ��s|S � d � d  � d  }|�d	��� }||d
 k }� d � d k}� d � d k }d}	d
� jv r\� d
 �d	��� }
� d
 |
d k}|}	|�d�| @ }||@ |	@ }
||@ |	@ }d|j|
< d|j|< |S )zL
        Generate signals based on Bollinger Band squeeze breakouts
        r   r   )�bb_upper�bb_lower�	bb_middlec                 3   r4   r5   r6   r7   r:   r	   r
   r;   }   r<   zOProfitableSignalGenerator.generate_bollinger_squeeze_signals.<locals>.<genexpr>rD   rE   rF   r   g�������?r   T�Volumeg333333�?r   r   )	r   r   r   r=   r   r   r   r   r   )r   r   r   �bb_cols�bb_width�bb_width_ma�squeeze�price_above_upper�price_below_lower�volume_confirmation�	volume_ma�high_volume�
squeeze_endedr.   r/   r	   r:   r
   �"generate_bollinger_squeeze_signalsv   s(   


z<ProfitableSignalGenerator.generate_bollinger_squeeze_signalsc                 C   s  t jd|jd�}d|jvr|d �d��� |d< d|jvr(|d �d��� |d< |d |d k|d |d k@ }|d |d k |d |d k @ }||d �d�|d �d�k @ |d |d k@ }||d �d�|d �d�k@ |d |d k @ }d|j|< d	|j|< |S )
zW
        Generate trend continuation signals using multiple timeframe analysis
        r   r   r   r   r   r   r   r   r   r   )r   r   r   �uptrend�	downtrend�pullback_buy�bounce_sellr	   r	   r
   �#generate_trend_continuation_signals�   s,   

  ������

z=ProfitableSignalGenerator.generate_trend_continuation_signalsc                 C   s4  t d� | �|�}| �|�}| �|�}| �|�}| �|�}|d |d  |d  |d  |d  }tjd|jd�}d}	d	}
d
|j	||	k< d|j	||
k< | �
||�}|d
k�� }|dk�� }|| }
|
dkr~t d� | �|�}|d
k�� }|dk�� }|| }
t d
|� d|� d�� t d|
t
|� d d�d�� |S )zX
        Generate high-probability profitable signals using multiple strategies
        u-   💰 Generating profitable trading signals...g333333�?g�������?g      �?g333333�?g�������?r   r   g������ɿr   r   uE   ⚠️  No signals from advanced strategies, using simple fallback...u   ✅ Generated �	 buy and �
 sell signalsz   Signal rate: �d   �.1f�%)�printr$   r0   rC   rR   rW   r   r   r   r   �_filter_signals_for_profit�sum�!_generate_simple_fallback_signals�len)r   r   �golden_cross_signals�rsi_signals�macd_signals�
bb_signals�
trend_signals�combined_score�
final_signals�
buy_threshold�sell_threshold�	buy_count�
sell_count�
total_signalsr	   r	   r
   �generate_profitable_signals�   sD   




�����	
z5ProfitableSignalGenerator.generate_profitable_signalsr   c           
      C   s�   |� � }d}tt|��D ]}|j| dkr(|dur&|| | jk r&d|j|< q|}qd|jv rD|d �d��� }|d |d k }d|j|< d|jv rtd|jv rt|d |d d	 k}|d |d d
 k }	d|j||dk@ < d|j|	|dk@ < |S )
z9
        Filter signals to improve profitability
        Nr   �atrr   gffffff�?rD   rE   r   g\���(\�?gR���Q�?r   r   )	�copy�rangera   �ilocr   r   r   r   r   )
r   r   r   �filtered_signals�last_signal_idx�i�atr_ma�low_volatility�
near_upper�
near_lowerr	   r	   r
   r^   �   s$   �

z4ProfitableSignalGenerator._filter_signals_for_profitc           	      C   sl  t jd|jd�}d|jvr|d �d��� |d< d|jvr(|d �d��� |d< d|jv rU|d d	k|d �d
�d	k@ }|d dk |d �d
�dk@ }d
|j|< d|j|< |dk�� dkr�|d |d �d
� d
 d }|dk}|dk }t	t
|��D ]9}|j| r�|dkr�|j|d |� dk�� r�d
|j|< qz|j| r�|dkr�|j|d |� dk�� r�d|j|< qz|S )zC
        Generate simple but effective signals as fallback
        r   r   r   r   r   r   r   r%   �   r   �F   r   �   rZ   g       @g       �r   )
r   r   r   r   r   r   r   r   r_   rq   ra   rr   r=   )	r   r   r   �rsi_buy�rsi_sell�price_change_3d�momentum_buy�
momentum_sellru   r	   r	   r
   r`     s0   





�
�z;ProfitableSignalGenerator._generate_simple_fallback_signalsN)�__name__�
__module__�__qualname__�__doc__r   r   �	DataFramer   r$   r0   rC   rR   rW   rn   r^   r`   r	   r	   r	   r
   r      s    !#'#5 r   r   r
   c              	   C   sf   t � }|�| �}| �� }||d< t�|d dkdt�|d dkdd��|d< t�|�d }||d	< |S )
z9
    Apply profitable signal generation to dataframe
    �signalr   zrgba(0,180,0,0.7)r   zrgba(180,0,0,0.7)zrgba(128,128,128,0.5)�bar_plot_colorg�������?�signal_strength)r   rn   rp   �np�where�abs)r   �
signal_gen�profitable_signals�	df_resultr�   r	   r	   r
   �apply_profitable_signals?  s   
��	r�   �__main__z+=== Testing Profitable Signal Generator ===z
2023-01-01��   �D)�periods�freq�*   g����Mb`?g{�G�z�?rZ   r   g���Q��?i�  i'  )�Open�High�Lowr   rG   r   r   r   r   r   r   �   r%   �   )�span�   r1   �	   r2   r3   rF   �   rD   rE   r�   r   z
Generated rX   rY   z
Signal rate: r[   r\   zGolden CrosszRSI Divergencez
MACD Momentumz
BB SqueezezTrend Continuationz: z signalsu.   ✅ Profitable signal generator test completed)8r�   �pandasr   �numpyr�   �typingr   r   �warnings�filterwarningsr   r�   r�   r�   r]   �
date_range�dates�random�seed�cumsum�normal�trend�exp�prices�uniform�randintr   r   r   �diff�deltar�   �gain�loss�rs�ewm�ema_12�ema_26�std�bb_std�	result_dfr_   r.   r/   ra   r�   r$   r0   rC   rR   rW   �
strategies�name�funcr   �countr	   r	   r	   r
   �<module>   sv    
  5
��	"��