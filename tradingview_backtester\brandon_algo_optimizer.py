"""
Brandon Algo Parameter Optimizer
Comprehensive parameter optimization system for Brandon Algo indicator and strategy

This module provides:
- Parameter range definitions for all Brandon Algo components
- Objective functions for optimization
- Integration with existing parameter optimization framework
- Performance analysis and comparison tools
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from brandon_algo_strategy import BrandonAlgoStrategy
from enhanced_backtester import EnhancedBacktester, BacktestConfig
from parameter_optimizer import ParameterOptimizer, ParameterRange

class BrandonAlgoOptimizer:
    """
    Brandon Algo Parameter Optimizer

    Optimizes all parameters of the Brandon Algo indicator and trading strategy
    for maximum trading accuracy and profitability.
    """

    def __init__(self, data: pd.DataFrame, backtest_config: BacktestConfig = None):
        """
        Initialize Brandon Algo Optimizer

        Args:
            data: Historical price data for optimization
            backtest_config: Backtesting configuration
        """
        self.data = data
        self.backtest_config = backtest_config or self._get_default_backtest_config()
        self.optimization_results = []

    def _get_default_backtest_config(self) -> BacktestConfig:
        """Get default backtesting configuration"""
        return BacktestConfig(
            initial_capital=10000,
            commission_rate=0.001,
            slippage_rate=0.0005,
            position_size_method="fixed_percentage",
            position_size_value=20.0,  # 20% of capital per trade
            stop_loss_percentage=0.04,  # 4% stop loss
            take_profit_percentage=0.08  # 8% take profit
        )

    def get_parameter_ranges(self) -> Dict[str, ParameterRange]:
        """
        Define parameter ranges for Brandon Algo optimization

        Returns:
            Dictionary of parameter ranges for optimization
        """
        return {
            # MACD Parameters
            'macd_fast_length': ParameterRange('macd_fast_length', 8, 16, 1),
            'macd_slow_length': ParameterRange('macd_slow_length', 20, 35, 1),
            'macd_signal_length': ParameterRange('macd_signal_length', 6, 12, 1),

            # RSI Parameters
            'rsi_length': ParameterRange('rsi_length', 10, 20, 1),
            'rsi_period1': ParameterRange('rsi_period1', 10, 20, 1),
            'rsi_period2': ParameterRange('rsi_period2', 14, 24, 1),
            'stoch_length': ParameterRange('stoch_length', 10, 20, 1),

            # DMI Parameters
            'dmi_length': ParameterRange('dmi_length', 10, 20, 1),
            'adx_threshold': ParameterRange('adx_threshold', 20, 35, 1),

            # Perfect Trail Parameters
            'perfect_trail_ma_length': ParameterRange('perfect_trail_ma_length', 50, 100, 5),
            'perfect_trail_atr_length': ParameterRange('perfect_trail_atr_length', 150, 250, 10),
            'perfect_trail_bands_dist': ParameterRange('perfect_trail_bands_dist', 2.0, 4.0, 0.2),

            # Weight Parameters
            'macd_weight': ParameterRange('macd_weight', 0.5, 2.0, 0.1),
            'rsi_weight': ParameterRange('rsi_weight', 0.5, 2.0, 0.1),
            'dmi_weight': ParameterRange('dmi_weight', 0.5, 2.0, 0.1),
            'dynamic_weight_strength': ParameterRange('dynamic_weight_strength', 0.2, 0.8, 0.1),

            # Strategy Parameters
            'min_confluence_score': ParameterRange('min_confluence_score', 0.2, 0.6, 0.1),
            'min_trend_quality': ParameterRange('min_trend_quality', 0.1, 0.5, 0.1),
            'min_signal_strength': ParameterRange('min_signal_strength', 1, 3, 1),

            # Risk Management
            'stop_loss_pct': ParameterRange('stop_loss_pct', 0.02, 0.06, 0.005),
            'take_profit_pct': ParameterRange('take_profit_pct', 0.04, 0.12, 0.01),

            # Volume and Sentiment Impact
            'volume_impact': ParameterRange('volume_impact', 0.1, 0.4, 0.05),
            'swing_impact': ParameterRange('swing_impact', 0.2, 0.5, 0.05),
            'sentiment_weight': ParameterRange('sentiment_weight', 0.1, 0.4, 0.05),
        }

    def create_objective_function(self, optimization_metric: str = 'total_pnl_percentage'):
        """
        Create objective function for Brandon Algo optimization

        Args:
            optimization_metric: Metric to optimize ('total_pnl_percentage', 'sharpe_ratio', 'win_rate', etc.)

        Returns:
            Objective function for parameter optimization
        """
        def objective_function(params: Dict) -> Dict:
            """
            Objective function for Brandon Algo parameter optimization

            Args:
                params: Dictionary of parameters to test

            Returns:
                Dictionary of performance metrics
            """
            try:
                # Create indicator configuration
                indicator_config = {
                    'macd_fast_length': params.get('macd_fast_length', 12),
                    'macd_slow_length': params.get('macd_slow_length', 26),
                    'macd_signal_length': params.get('macd_signal_length', 9),
                    'rsi_length': params.get('rsi_length', 14),
                    'rsi_period1': params.get('rsi_period1', 14),
                    'rsi_period2': params.get('rsi_period2', 17),
                    'stoch_length': params.get('stoch_length', 14),
                    'dmi_length': params.get('dmi_length', 14),
                    'adx_threshold': params.get('adx_threshold', 25),
                    'perfect_trail_ma_length': params.get('perfect_trail_ma_length', 70),
                    'perfect_trail_atr_length': params.get('perfect_trail_atr_length', 200),
                    'perfect_trail_bands_dist': params.get('perfect_trail_bands_dist', 3.0),
                    'macd_weight': params.get('macd_weight', 1.0),
                    'rsi_weight': params.get('rsi_weight', 1.0),
                    'dmi_weight': params.get('dmi_weight', 1.0),
                    'dynamic_weight_strength': params.get('dynamic_weight_strength', 0.45),
                    'volume_impact': params.get('volume_impact', 0.25),
                    'swing_impact': params.get('swing_impact', 0.35),
                    'sentiment_weight': params.get('sentiment_weight', 0.25),
                }

                # Create strategy configuration
                strategy_config = {
                    'indicator_config': indicator_config,
                    'min_confluence_score': params.get('min_confluence_score', 0.4),
                    'min_trend_quality': params.get('min_trend_quality', 0.3),
                    'min_signal_strength': params.get('min_signal_strength', 1),
                    'stop_loss_pct': params.get('stop_loss_pct', 0.04),
                    'take_profit_pct': params.get('take_profit_pct', 0.08),
                }

                # Create strategy and generate signals
                strategy = BrandonAlgoStrategy(strategy_config)
                signals_df = strategy.generate_signals(self.data.copy())

                # Convert Brandon Algo signals to standard format for backtesting
                signals_df['signal'] = 0
                signals_df.loc[signals_df['trading_signal'] == 'BUY', 'signal'] = 1
                signals_df.loc[signals_df['trading_signal'] == 'SELL', 'signal'] = -1

                # Run backtest
                backtester = EnhancedBacktester(self.backtest_config)
                results = backtester.run_backtest(signals_df)

                # Add Brandon Algo specific metrics
                brandon_summary = strategy.get_strategy_summary(signals_df)
                results.update({
                    'brandon_total_signals': brandon_summary['total_signals'],
                    'brandon_buy_signals': brandon_summary['buy_signals'],
                    'brandon_sell_signals': brandon_summary['sell_signals'],
                })

                # Penalize strategies with too few trades
                if results['total_trades'] < 3:
                    results['total_pnl_percentage'] *= 0.3

                # Bonus for good signal quality
                if 'confluence_score' in signals_df.columns:
                    avg_confluence = signals_df['confluence_score'].mean()
                    if avg_confluence > 0.5:
                        results['total_pnl_percentage'] *= 1.1

                if 'trend_quality' in signals_df.columns:
                    avg_quality = signals_df['trend_quality'].mean()
                    if avg_quality > 0.4:
                        results['total_pnl_percentage'] *= 1.1

                return results

            except Exception as e:
                print(f"Error in objective function: {e}")
                return {
                    'total_pnl_percentage': -100,
                    'success_rate': 0,
                    'total_trades': 0,
                    'sharpe_ratio': 0,
                    'max_drawdown': 100,
                    'brandon_total_signals': 0
                }

        return objective_function

    def run_optimization(self, optimization_method: str = 'grid_search',
                        max_iterations: int = 1000,
                        optimization_metric: str = 'total_pnl_percentage') -> Dict:
        """
        Run Brandon Algo parameter optimization

        Args:
            optimization_method: Method to use ('grid_search', 'random_search', 'genetic_algorithm')
            max_iterations: Maximum number of iterations
            optimization_metric: Metric to optimize

        Returns:
            Optimization results with best parameters
        """
        print(f"🚀 Starting Brandon Algo optimization using {optimization_method}")
        print(f"   Optimization metric: {optimization_metric}")
        print(f"   Max iterations: {max_iterations}")
        print(f"   Data period: {self.data.index[0]} to {self.data.index[-1]}")
        print(f"   Total data points: {len(self.data)}")

        # Get parameter ranges
        param_ranges = self.get_parameter_ranges()

        # Create objective function
        objective_func = self.create_objective_function(optimization_metric)

        # Convert parameter ranges to list format
        param_ranges_list = [param_ranges[key] for key in param_ranges.keys()]

        # Initialize parameter optimizer
        optimizer = ParameterOptimizer(
            objective_function=objective_func,
            scoring_metric=optimization_metric
        )

        # Run optimization based on method
        if optimization_method == 'grid_search':
            optimization_results = optimizer.grid_search(param_ranges_list, max_combinations=max_iterations)
        elif optimization_method == 'random_search':
            optimization_results = optimizer.random_search(param_ranges_list, n_iterations=max_iterations)
        elif optimization_method == 'genetic_algorithm':
            optimization_results = optimizer.genetic_algorithm(
                param_ranges_list,
                population_size=50,
                generations=max_iterations // 50,
                mutation_rate=0.1,
                crossover_rate=0.8
            )
        else:
            raise ValueError(f"Unknown optimization method: {optimization_method}")

        # Convert results to expected format
        if optimization_results:
            best_result = optimization_results[0]
            results = {
                'best_parameters': best_result.parameters,
                'best_score': best_result.score,
                'total_evaluations': len(optimization_results),
                'optimization_metric': optimization_metric,
                'all_results': optimization_results
            }
        else:
            results = {
                'best_parameters': {},
                'best_score': 0,
                'total_evaluations': 0,
                'optimization_metric': optimization_metric,
                'all_results': []
            }

        # Store results
        self.optimization_results.append({
            'method': optimization_method,
            'metric': optimization_metric,
            'results': results,
            'timestamp': pd.Timestamp.now()
        })

        # Print summary
        best_params = results['best_parameters']
        best_score = results['best_score']

        print(f"\n✅ Optimization completed!")
        print(f"   Best {optimization_metric}: {best_score:.4f}")
        print(f"   Total evaluations: {results.get('total_evaluations', 'N/A')}")
        print(f"   Best parameters:")
        for param, value in best_params.items():
            print(f"     {param}: {value}")

        return results

    def compare_parameter_sets(self, parameter_sets: List[Dict],
                              set_names: List[str] = None) -> pd.DataFrame:
        """
        Compare different parameter sets

        Args:
            parameter_sets: List of parameter dictionaries to compare
            set_names: Names for each parameter set

        Returns:
            DataFrame with comparison results
        """
        if set_names is None:
            set_names = [f"Set_{i+1}" for i in range(len(parameter_sets))]

        print(f"📊 Comparing {len(parameter_sets)} parameter sets...")

        objective_func = self.create_objective_function()
        comparison_results = []

        for i, (params, name) in enumerate(zip(parameter_sets, set_names)):
            print(f"   Testing {name}...")

            try:
                results = objective_func(params)
                results['parameter_set'] = name
                results['parameters'] = params
                comparison_results.append(results)

            except Exception as e:
                print(f"   Error testing {name}: {e}")
                continue

        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_results)

        # Sort by total PnL percentage
        comparison_df = comparison_df.sort_values('total_pnl_percentage', ascending=False)

        print(f"\n📈 Parameter Set Comparison Results:")
        print("=" * 80)

        for _, row in comparison_df.iterrows():
            print(f"{row['parameter_set']:>10}: "
                  f"PnL: {row['total_pnl_percentage']:>8.2f}% | "
                  f"Win Rate: {row['success_rate']:>6.1f}% | "
                  f"Trades: {row['total_trades']:>3.0f} | "
                  f"Sharpe: {row['sharpe_ratio']:>6.2f} | "
                  f"Max DD: {row['max_drawdown']:>6.2f}%")

        return comparison_df

    def get_optimized_config(self, optimization_results: Dict = None) -> Dict:
        """
        Get optimized configuration for Brandon Algo

        Args:
            optimization_results: Results from optimization (uses latest if None)

        Returns:
            Optimized configuration dictionary
        """
        if optimization_results is None:
            if not self.optimization_results:
                raise ValueError("No optimization results available. Run optimization first.")
            optimization_results = self.optimization_results[-1]['results']

        best_params = optimization_results['best_parameters']

        # Create optimized indicator configuration
        indicator_config = {
            'macd_fast_length': best_params.get('macd_fast_length', 12),
            'macd_slow_length': best_params.get('macd_slow_length', 26),
            'macd_signal_length': best_params.get('macd_signal_length', 9),
            'rsi_length': best_params.get('rsi_length', 14),
            'rsi_period1': best_params.get('rsi_period1', 14),
            'rsi_period2': best_params.get('rsi_period2', 17),
            'stoch_length': best_params.get('stoch_length', 14),
            'dmi_length': best_params.get('dmi_length', 14),
            'adx_threshold': best_params.get('adx_threshold', 25),
            'perfect_trail_ma_length': best_params.get('perfect_trail_ma_length', 70),
            'perfect_trail_atr_length': best_params.get('perfect_trail_atr_length', 200),
            'perfect_trail_bands_dist': best_params.get('perfect_trail_bands_dist', 3.0),
            'macd_weight': best_params.get('macd_weight', 1.0),
            'rsi_weight': best_params.get('rsi_weight', 1.0),
            'dmi_weight': best_params.get('dmi_weight', 1.0),
            'dynamic_weight_strength': best_params.get('dynamic_weight_strength', 0.45),
            'volume_impact': best_params.get('volume_impact', 0.25),
            'swing_impact': best_params.get('swing_impact', 0.35),
            'sentiment_weight': best_params.get('sentiment_weight', 0.25),
        }

        # Create optimized strategy configuration
        strategy_config = {
            'indicator_config': indicator_config,
            'min_confluence_score': best_params.get('min_confluence_score', 0.4),
            'min_trend_quality': best_params.get('min_trend_quality', 0.3),
            'min_signal_strength': best_params.get('min_signal_strength', 1),
            'stop_loss_pct': best_params.get('stop_loss_pct', 0.04),
            'take_profit_pct': best_params.get('take_profit_pct', 0.08),
        }

        return {
            'indicator_config': indicator_config,
            'strategy_config': strategy_config,
            'optimization_score': optimization_results['best_score'],
            'optimization_metric': optimization_results.get('optimization_metric', 'total_pnl_percentage')
        }

    def generate_optimization_report(self, save_path: str = None) -> str:
        """
        Generate comprehensive optimization report

        Args:
            save_path: Path to save the report (optional)

        Returns:
            Report as string
        """
        if not self.optimization_results:
            return "No optimization results available."

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("BRANDON ALGO PARAMETER OPTIMIZATION REPORT")
        report_lines.append("=" * 80)
        report_lines.append("")

        # Data summary
        report_lines.append("DATA SUMMARY:")
        report_lines.append(f"  Period: {self.data.index[0]} to {self.data.index[-1]}")
        report_lines.append(f"  Total bars: {len(self.data)}")
        report_lines.append(f"  Symbol: {getattr(self.data, 'symbol', 'Unknown')}")
        report_lines.append("")

        # Optimization results
        for i, opt_result in enumerate(self.optimization_results):
            report_lines.append(f"OPTIMIZATION RUN #{i+1}:")
            report_lines.append(f"  Method: {opt_result['method']}")
            report_lines.append(f"  Metric: {opt_result['metric']}")
            report_lines.append(f"  Timestamp: {opt_result['timestamp']}")
            report_lines.append(f"  Best Score: {opt_result['results']['best_score']:.4f}")
            report_lines.append("")

            # Best parameters
            report_lines.append("  Best Parameters:")
            best_params = opt_result['results']['best_parameters']
            for param, value in sorted(best_params.items()):
                report_lines.append(f"    {param}: {value}")
            report_lines.append("")

        # Latest optimized configuration
        if self.optimization_results:
            optimized_config = self.get_optimized_config()
            report_lines.append("RECOMMENDED CONFIGURATION:")
            report_lines.append(f"  Optimization Score: {optimized_config['optimization_score']:.4f}")
            report_lines.append(f"  Optimization Metric: {optimized_config['optimization_metric']}")
            report_lines.append("")

            report_lines.append("  Indicator Configuration:")
            for param, value in sorted(optimized_config['indicator_config'].items()):
                report_lines.append(f"    {param}: {value}")
            report_lines.append("")

            report_lines.append("  Strategy Configuration:")
            for param, value in sorted(optimized_config['strategy_config'].items()):
                if param != 'indicator_config':
                    report_lines.append(f"    {param}: {value}")
            report_lines.append("")

        report_lines.append("=" * 80)

        report = "\n".join(report_lines)

        if save_path:
            with open(save_path, 'w') as f:
                f.write(report)
            print(f"📄 Report saved to: {save_path}")

        return report