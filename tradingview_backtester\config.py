"""
Configuration file for Enhanced Trading Application
Centralized settings and parameters
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
import os

@dataclass
class DataConfig:
    """Data fetching configuration"""
    default_symbol: str = "BTCUSDT"
    default_interval: str = "1d"
    default_limit: int = 200
    data_sources: List[str] = None
    cache_duration: int = 300  # seconds
    
    def __post_init__(self):
        if self.data_sources is None:
            self.data_sources = ["binance", "yahoo", "coingecko"]

@dataclass
class IndicatorConfig:
    """Technical indicator configuration"""
    # RSI
    rsi_length: int = 14
    rsi_overbought: float = 70
    rsi_oversold: float = 30
    
    # Bollinger Bands
    bb_length: int = 20
    bb_std_dev: float = 2.0
    
    # MACD
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    
    # Stochastic
    stoch_k_period: int = 14
    stoch_d_period: int = 3
    stoch_overbought: float = 80
    stoch_oversold: float = 20
    
    # SuperTrend
    supertrend_period: int = 10
    supertrend_multiplier: float = 3.0
    
    # ATR
    atr_period: int = 14
    
    # Williams %R
    williams_period: int = 14
    
    # Money Flow Index
    mfi_period: int = 14
    
    # Perfect Trail (Custom)
    pt_ma_type: str = "ema"
    pt_ma_length: int = 20
    pt_atr_length: int = 14
    pt_bands_dist: float = 2.0
    pt_band2_mult: float = 1.5
    pt_band3_mult: float = 3.0
    pt_confirm_bars: int = 1
    
    # Nadaraya-Watson Envelope (Custom)
    nwe_h: float = 8.0
    nwe_mult: float = 3.0
    nwe_window_size: int = 50
    nwe_src_column: str = "Close"
    
    # DMI/ADX
    dmi_length: int = 14
    use_dmi: bool = True

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    initial_capital: float = 10000.0
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    
    # Position sizing
    position_size_method: str = "fixed_percentage"  # 'fixed_amount', 'fixed_percentage', 'kelly'
    position_size_value: float = 10.0  # 10% of capital
    
    # Risk management
    stop_loss_percentage: Optional[float] = None  # e.g., 0.05 for 5%
    take_profit_percentage: Optional[float] = None  # e.g., 0.10 for 10%
    max_positions: int = 1
    allow_short: bool = False
    
    # Advanced settings
    use_realistic_slippage: bool = True
    account_for_spread: bool = True
    minimum_trade_size: float = 10.0  # Minimum trade size in base currency

@dataclass
class OptimizationConfig:
    """Parameter optimization configuration"""
    method: str = "random"  # 'grid', 'random', 'genetic'
    iterations: int = 100
    scoring_metric: str = "total_pnl_percentage"
    
    # Genetic algorithm specific
    population_size: int = 50
    generations: int = 20
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    
    # Walk-forward analysis
    train_periods: int = 252  # 1 year of daily data
    test_periods: int = 63    # 3 months of daily data
    step_size: int = 21       # 1 month step
    
    # Parameter ranges for optimization
    parameter_ranges: Dict = None
    
    def __post_init__(self):
        if self.parameter_ranges is None:
            self.parameter_ranges = {
                'rsi_length': {'min': 10, 'max': 30, 'type': 'int'},
                'bb_length': {'min': 15, 'max': 25, 'type': 'int'},
                'bb_std_dev': {'min': 1.5, 'max': 2.5, 'type': 'float', 'step': 0.1},
                'macd_fast': {'min': 8, 'max': 16, 'type': 'int'},
                'macd_slow': {'min': 20, 'max': 30, 'type': 'int'},
                'pt_ma_length': {'min': 10, 'max': 50, 'type': 'int'},
                'supertrend_period': {'min': 5, 'max': 20, 'type': 'int'},
                'supertrend_multiplier': {'min': 2.0, 'max': 4.0, 'type': 'float', 'step': 0.1}
            }

@dataclass
class ChartConfig:
    """Chart and visualization configuration"""
    default_height: int = 800
    default_width: int = 1200
    
    # Colors
    bullish_color: str = "rgba(0,180,0,0.7)"
    bearish_color: str = "rgba(180,0,0,0.7)"
    neutral_color: str = "rgba(128,128,128,0.5)"
    
    # Indicators to show by default
    default_indicators: List[str] = None
    
    # Chart layout
    show_volume: bool = True
    show_signals: bool = True
    show_oscillators: bool = True
    
    def __post_init__(self):
        if self.default_indicators is None:
            self.default_indicators = [
                'bb_upper', 'bb_lower', 'supertrend', 'vwap'
            ]

@dataclass
class DashboardConfig:
    """Dashboard configuration"""
    title: str = "Enhanced Trading Dashboard"
    page_icon: str = "📈"
    layout: str = "wide"
    
    # Auto-refresh settings
    auto_refresh_interval: int = 30  # seconds
    enable_auto_refresh: bool = False
    
    # Cache settings
    data_cache_ttl: int = 300  # 5 minutes
    indicator_cache_ttl: int = 60  # 1 minute
    
    # Display settings
    show_advanced_metrics: bool = True
    show_trade_details: bool = True
    show_parameter_sensitivity: bool = False

class AppConfig:
    """Main application configuration"""
    
    def __init__(self):
        self.data = DataConfig()
        self.indicators = IndicatorConfig()
        self.backtest = BacktestConfig()
        self.optimization = OptimizationConfig()
        self.chart = ChartConfig()
        self.dashboard = DashboardConfig()
        
        # Load from environment variables if available
        self._load_from_env()
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # Data configuration
        if os.getenv('DEFAULT_SYMBOL'):
            self.data.default_symbol = os.getenv('DEFAULT_SYMBOL')
        
        if os.getenv('DEFAULT_INTERVAL'):
            self.data.default_interval = os.getenv('DEFAULT_INTERVAL')
        
        if os.getenv('DEFAULT_LIMIT'):
            self.data.default_limit = int(os.getenv('DEFAULT_LIMIT'))
        
        # Backtest configuration
        if os.getenv('INITIAL_CAPITAL'):
            self.backtest.initial_capital = float(os.getenv('INITIAL_CAPITAL'))
        
        if os.getenv('COMMISSION_RATE'):
            self.backtest.commission_rate = float(os.getenv('COMMISSION_RATE'))
        
        if os.getenv('STOP_LOSS_PCT'):
            self.backtest.stop_loss_percentage = float(os.getenv('STOP_LOSS_PCT')) / 100
        
        if os.getenv('TAKE_PROFIT_PCT'):
            self.backtest.take_profit_percentage = float(os.getenv('TAKE_PROFIT_PCT')) / 100
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary"""
        return {
            'data': self.data.__dict__,
            'indicators': self.indicators.__dict__,
            'backtest': self.backtest.__dict__,
            'optimization': self.optimization.__dict__,
            'chart': self.chart.__dict__,
            'dashboard': self.dashboard.__dict__
        }
    
    def get_custom_indicator_params(self) -> Dict:
        """Get parameters for custom indicators"""
        return {
            'rsi_length': self.indicators.rsi_length,
            'pt_ma_type': self.indicators.pt_ma_type,
            'pt_ma_length': self.indicators.pt_ma_length,
            'pt_atr_length': self.indicators.pt_atr_length,
            'pt_bands_dist': self.indicators.pt_bands_dist,
            'pt_band2_mult': self.indicators.pt_band2_mult,
            'pt_band3_mult': self.indicators.pt_band3_mult,
            'pt_confirm_bars': self.indicators.pt_confirm_bars,
            'macd_fast_length': self.indicators.macd_fast,
            'macd_slow_length': self.indicators.macd_slow,
            'macd_signal_length': self.indicators.macd_signal,
            'nwe_src_column': self.indicators.nwe_src_column,
            'nwe_h': self.indicators.nwe_h,
            'nwe_mult': self.indicators.nwe_mult,
            'nwe_window_size': self.indicators.nwe_window_size,
            'dmi_length': self.indicators.dmi_length,
            'useDmi': self.indicators.use_dmi
        }
    
    def get_enhanced_indicator_params(self) -> Dict:
        """Get parameters for enhanced indicators"""
        return {
            'bb_length': self.indicators.bb_length,
            'bb_std': self.indicators.bb_std_dev,
            'stoch_k': self.indicators.stoch_k_period,
            'stoch_d': self.indicators.stoch_d_period,
            'williams_period': self.indicators.williams_period,
            'atr_period': self.indicators.atr_period,
            'supertrend_period': self.indicators.supertrend_period,
            'supertrend_mult': self.indicators.supertrend_multiplier,
            'mfi_period': self.indicators.mfi_period
        }

# Global configuration instance
config = AppConfig()

# Convenience functions
def get_config() -> AppConfig:
    """Get the global configuration instance"""
    return config

def update_config(**kwargs):
    """Update configuration parameters"""
    global config
    
    for section, params in kwargs.items():
        if hasattr(config, section):
            section_config = getattr(config, section)
            for key, value in params.items():
                if hasattr(section_config, key):
                    setattr(section_config, key, value)

# Example usage and testing
if __name__ == '__main__':
    print("=== Testing Configuration ===")
    
    # Test default configuration
    app_config = AppConfig()
    print(f"Default symbol: {app_config.data.default_symbol}")
    print(f"Default RSI length: {app_config.indicators.rsi_length}")
    print(f"Default initial capital: ${app_config.backtest.initial_capital}")
    
    # Test custom indicator parameters
    custom_params = app_config.get_custom_indicator_params()
    print(f"\nCustom indicator parameters: {len(custom_params)} items")
    
    # Test enhanced indicator parameters
    enhanced_params = app_config.get_enhanced_indicator_params()
    print(f"Enhanced indicator parameters: {len(enhanced_params)} items")
    
    # Test configuration update
    update_config(
        data={'default_symbol': 'ETHUSDT'},
        indicators={'rsi_length': 21},
        backtest={'initial_capital': 20000}
    )
    
    print(f"\nAfter update:")
    print(f"Symbol: {config.data.default_symbol}")
    print(f"RSI length: {config.indicators.rsi_length}")
    print(f"Initial capital: ${config.backtest.initial_capital}")
    
    print("\n✅ Configuration test completed")
