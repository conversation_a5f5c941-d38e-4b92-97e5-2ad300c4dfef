#!/usr/bin/env python3
"""
Comprehensive test script for the Enhanced Trading Application
Tests all major components and functionality
"""

import sys
import traceback
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_imports():
    """Test if all modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from enhanced_data_fetcher import EnhancedDataFetcher
        from enhanced_backtester import EnhancedBacktester, BacktestConfig
        from parameter_optimizer import ParameterOptimizer, ParameterRange
        from enhanced_indicators import EnhancedIndicators
        from config import AppConfig
        print("✅ All enhanced modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_fetcher():
    """Test the enhanced data fetcher"""
    print("\n📊 Testing Enhanced Data Fetcher...")
    
    try:
        from enhanced_data_fetcher import EnhancedDataFetcher
        
        fetcher = EnhancedDataFetcher()
        
        # Test with sample data (should always work)
        print("  Testing sample data generation...")
        sample_data = fetcher._generate_sample_data("BTCUSDT", 50)
        
        if sample_data is not None and len(sample_data) > 0:
            print(f"  ✅ Sample data: {len(sample_data)} records")
            
            # Test data validation
            is_valid, issues = fetcher.validate_data(sample_data)
            if is_valid:
                print("  ✅ Data validation passed")
            else:
                print(f"  ⚠️  Data validation issues: {issues}")
            
            return True
        else:
            print("  ❌ Failed to generate sample data")
            return False
            
    except Exception as e:
        print(f"  ❌ Data fetcher error: {e}")
        traceback.print_exc()
        return False

def test_indicators():
    """Test the enhanced indicators"""
    print("\n🔧 Testing Enhanced Indicators...")
    
    try:
        from enhanced_indicators import EnhancedIndicators
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(99):
            change = np.random.normal(0, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 100)
        }, index=dates)
        
        # Test indicator calculation
        print("  Testing indicator calculations...")
        enhanced_df = EnhancedIndicators.calculate_all_indicators(df)
        
        original_cols = len(df.columns)
        enhanced_cols = len(enhanced_df.columns)
        new_indicators = enhanced_cols - original_cols
        
        print(f"  ✅ Added {new_indicators} indicators ({original_cols} → {enhanced_cols} columns)")
        
        # Test specific indicators
        expected_indicators = ['bb_upper', 'bb_lower', 'stoch_k', 'supertrend', 'rsi']
        found_indicators = [ind for ind in expected_indicators if ind in enhanced_df.columns]
        
        print(f"  ✅ Found {len(found_indicators)}/{len(expected_indicators)} expected indicators")
        
        return len(found_indicators) > 0
        
    except Exception as e:
        print(f"  ❌ Indicators error: {e}")
        traceback.print_exc()
        return False

def test_backtester():
    """Test the enhanced backtester"""
    print("\n💰 Testing Enhanced Backtester...")
    
    try:
        from enhanced_backtester import EnhancedBacktester, BacktestConfig
        
        # Create sample data with signals
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [100]
        for _ in range(49):
            change = np.random.normal(0, 0.02)
            prices.append(prices[-1] * (1 + change))
        
        df = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 50)
        }, index=dates)
        
        # Add simple signals
        df['signal'] = 0
        df.loc[df.index[::10], 'signal'] = 1   # Buy every 10 days
        df.loc[df.index[5::10], 'signal'] = -1  # Sell 5 days later
        
        # Test backtesting
        print("  Testing backtest execution...")
        config = BacktestConfig(
            initial_capital=10000,
            commission_rate=0.001,
            stop_loss_percentage=0.05
        )
        
        backtester = EnhancedBacktester(config)
        results = backtester.run_backtest(df)
        
        print(f"  ✅ Backtest completed:")
        print(f"    • Trades: {results['total_trades']}")
        print(f"    • Success Rate: {results['success_rate']}%")
        print(f"    • Total P&L: {results['total_pnl_percentage']}%")
        
        return results['total_trades'] >= 0  # Should have some trades or none
        
    except Exception as e:
        print(f"  ❌ Backtester error: {e}")
        traceback.print_exc()
        return False

def test_optimizer():
    """Test the parameter optimizer"""
    print("\n🎯 Testing Parameter Optimizer...")
    
    try:
        from parameter_optimizer import ParameterOptimizer, ParameterRange
        
        # Mock objective function
        def mock_objective(params):
            score = (
                params.get('param1', 10) * 0.1 +
                params.get('param2', 20) * 0.05 +
                np.random.random() * 5
            )
            return {
                'total_pnl_percentage': score,
                'success_rate': np.random.uniform(40, 80),
                'total_trades': np.random.randint(10, 50)
            }
        
        # Define parameter ranges
        param_ranges = [
            ParameterRange('param1', 5, 15, param_type='int'),
            ParameterRange('param2', 10, 30, param_type='int')
        ]
        
        print("  Testing random search optimization...")
        optimizer = ParameterOptimizer(mock_objective, 'total_pnl_percentage')
        results = optimizer.random_search(param_ranges, n_iterations=10)
        
        if results and len(results) > 0:
            best_result = results[0]
            print(f"  ✅ Optimization completed:")
            print(f"    • Best score: {best_result.score:.2f}")
            print(f"    • Best params: {best_result.parameters}")
            return True
        else:
            print("  ❌ No optimization results")
            return False
            
    except Exception as e:
        print(f"  ❌ Optimizer error: {e}")
        traceback.print_exc()
        return False

def test_config():
    """Test the configuration system"""
    print("\n⚙️ Testing Configuration System...")
    
    try:
        from config import AppConfig, get_config, update_config
        
        # Test default configuration
        config = AppConfig()
        print(f"  ✅ Default config loaded:")
        print(f"    • Symbol: {config.data.default_symbol}")
        print(f"    • RSI Length: {config.indicators.rsi_length}")
        print(f"    • Initial Capital: ${config.backtest.initial_capital}")
        
        # Test parameter extraction
        custom_params = config.get_custom_indicator_params()
        enhanced_params = config.get_enhanced_indicator_params()
        
        print(f"  ✅ Parameter extraction:")
        print(f"    • Custom params: {len(custom_params)} items")
        print(f"    • Enhanced params: {len(enhanced_params)} items")
        
        # Test configuration update
        update_config(
            data={'default_symbol': 'ETHUSDT'},
            indicators={'rsi_length': 21}
        )
        
        updated_config = get_config()
        print(f"  ✅ Configuration update:")
        print(f"    • Updated symbol: {updated_config.data.default_symbol}")
        print(f"    • Updated RSI: {updated_config.indicators.rsi_length}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Config error: {e}")
        traceback.print_exc()
        return False

def test_integration():
    """Test integration between components"""
    print("\n🔗 Testing Component Integration...")
    
    try:
        from enhanced_main import TradingApplication
        
        print("  Testing TradingApplication initialization...")
        app = TradingApplication()
        
        print("  ✅ Application initialized successfully")
        print(f"    • Data fetcher: {type(app.data_fetcher).__name__}")
        print(f"    • Indicators: {type(app.indicators).__name__}")
        print(f"    • MTF analysis: {type(app.mtf_analysis).__name__}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Integration error: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("=" * 60)
    print("🧪 ENHANCED TRADING APPLICATION - COMPREHENSIVE TESTS")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Data Fetcher", test_data_fetcher),
        ("Indicators", test_indicators),
        ("Backtester", test_backtester),
        ("Optimizer", test_optimizer),
        ("Configuration", test_config),
        ("Integration", test_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{test_name:.<20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! The enhanced trading application is ready to use.")
        print("\n🚀 Next steps:")
        print("  1. Run the dashboard: python run_dashboard.py")
        print("  2. Or run analysis: python enhanced_main.py")
        print("  3. Check the documentation in README.md")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("  Some features may not work correctly.")
    
    return passed == total

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
