"""
Enhanced Backtesting Framework
Includes position sizing, stop-loss, take-profit, slippage, and advanced metrics
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

@dataclass
class Trade:
    """Data class to represent a single trade"""
    entry_time: datetime
    entry_price: float
    quantity: float
    side: str  # 'long' or 'short'
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    pnl: Optional[float] = None
    pnl_percentage: Optional[float] = None
    commission: float = 0.0
    slippage: float = 0.0
    exit_reason: str = "signal"  # 'signal', 'stop_loss', 'take_profit'
    status: str = "open"  # 'open', 'closed'

@dataclass
class BacktestConfig:
    """Configuration for backtesting parameters"""
    initial_capital: float = 10000.0
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    position_size_method: str = "fixed_amount"  # 'fixed_amount', 'fixed_percentage', 'kelly'
    position_size_value: float = 1000.0  # Amount or percentage
    stop_loss_percentage: Optional[float] = None  # e.g., 0.02 for 2%
    take_profit_percentage: Optional[float] = None  # e.g., 0.04 for 4%
    max_positions: int = 1
    allow_short: bool = False

class EnhancedBacktester:
    """
    Enhanced backtesting engine with advanced features
    """
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.reset()
    
    def reset(self):
        """Reset backtester state"""
        self.capital = self.config.initial_capital
        self.positions = []
        self.closed_trades = []
        self.equity_curve = []
        self.drawdown_curve = []
        self.peak_equity = self.config.initial_capital
        
    def calculate_position_size(self, price: float, signal_strength: float = 1.0) -> float:
        """Calculate position size based on configuration"""
        if self.config.position_size_method == "fixed_amount":
            return self.config.position_size_value / price
        elif self.config.position_size_method == "fixed_percentage":
            return (self.capital * self.config.position_size_value / 100) / price
        elif self.config.position_size_method == "kelly":
            # Simplified Kelly criterion (would need historical win rate and avg win/loss)
            kelly_fraction = min(0.25, signal_strength * 0.1)  # Cap at 25%
            return (self.capital * kelly_fraction) / price
        else:
            return self.config.position_size_value / price
    
    def apply_slippage_and_commission(self, price: float, quantity: float, side: str) -> Tuple[float, float, float]:
        """Apply slippage and commission to a trade"""
        # Slippage: assume we get worse prices
        if side == "buy":
            slipped_price = price * (1 + self.config.slippage_rate)
        else:
            slipped_price = price * (1 - self.config.slippage_rate)
        
        # Commission
        trade_value = slipped_price * quantity
        commission = trade_value * self.config.commission_rate
        
        slippage_cost = abs(slipped_price - price) * quantity
        
        return slipped_price, commission, slippage_cost
    
    def check_stop_loss_take_profit(self, position: Trade, current_price: float) -> Optional[str]:
        """Check if position should be closed due to stop loss or take profit"""
        if position.side == "long":
            # Stop loss check
            if (self.config.stop_loss_percentage and 
                current_price <= position.entry_price * (1 - self.config.stop_loss_percentage)):
                return "stop_loss"
            
            # Take profit check
            if (self.config.take_profit_percentage and 
                current_price >= position.entry_price * (1 + self.config.take_profit_percentage)):
                return "take_profit"
        
        elif position.side == "short":
            # Stop loss check (price going up)
            if (self.config.stop_loss_percentage and 
                current_price >= position.entry_price * (1 + self.config.stop_loss_percentage)):
                return "stop_loss"
            
            # Take profit check (price going down)
            if (self.config.take_profit_percentage and 
                current_price <= position.entry_price * (1 - self.config.take_profit_percentage)):
                return "take_profit"
        
        return None
    
    def open_position(self, timestamp: datetime, price: float, signal: int, signal_strength: float = 1.0):
        """Open a new position"""
        if len(self.positions) >= self.config.max_positions:
            return
        
        side = "long" if signal > 0 else "short"
        if side == "short" and not self.config.allow_short:
            return
        
        quantity = self.calculate_position_size(price, signal_strength)
        
        # Check if we have enough capital
        required_capital = quantity * price
        if required_capital > self.capital:
            quantity = self.capital / price * 0.95  # Use 95% to leave some buffer
        
        if quantity <= 0:
            return
        
        # Apply slippage and commission
        actual_price, commission, slippage_cost = self.apply_slippage_and_commission(
            price, quantity, "buy" if side == "long" else "sell"
        )
        
        # Create trade
        trade = Trade(
            entry_time=timestamp,
            entry_price=actual_price,
            quantity=quantity,
            side=side,
            commission=commission,
            slippage=slippage_cost
        )
        
        # Update capital
        trade_cost = actual_price * quantity + commission + slippage_cost
        self.capital -= trade_cost
        
        self.positions.append(trade)
        
    def close_position(self, position: Trade, timestamp: datetime, price: float, reason: str = "signal"):
        """Close an existing position"""
        # Apply slippage and commission for exit
        actual_price, commission, slippage_cost = self.apply_slippage_and_commission(
            price, position.quantity, "sell" if position.side == "long" else "buy"
        )
        
        # Calculate P&L
        if position.side == "long":
            pnl = (actual_price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - actual_price) * position.quantity
        
        # Subtract exit costs
        pnl -= (commission + slippage_cost + position.commission + position.slippage)
        
        # Update position
        position.exit_time = timestamp
        position.exit_price = actual_price
        position.pnl = pnl
        position.pnl_percentage = (pnl / (position.entry_price * position.quantity)) * 100
        position.commission += commission
        position.slippage += slippage_cost
        position.exit_reason = reason
        position.status = "closed"
        
        # Update capital
        proceeds = actual_price * position.quantity - commission - slippage_cost
        self.capital += proceeds
        
        # Move to closed trades
        self.closed_trades.append(position)
        self.positions.remove(position)
    
    def update_equity_curve(self, timestamp: datetime, current_prices: Dict[str, float]):
        """Update equity curve with current market values"""
        # Calculate unrealized P&L
        unrealized_pnl = 0
        for position in self.positions:
            if position.side == "long":
                unrealized_pnl += (current_prices.get('close', position.entry_price) - position.entry_price) * position.quantity
            else:
                unrealized_pnl += (position.entry_price - current_prices.get('close', position.entry_price)) * position.quantity
        
        total_equity = self.capital + unrealized_pnl
        
        # Update peak and drawdown
        if total_equity > self.peak_equity:
            self.peak_equity = total_equity
        
        drawdown = (self.peak_equity - total_equity) / self.peak_equity * 100
        
        self.equity_curve.append({
            'timestamp': timestamp,
            'equity': total_equity,
            'capital': self.capital,
            'unrealized_pnl': unrealized_pnl
        })
        
        self.drawdown_curve.append({
            'timestamp': timestamp,
            'drawdown': drawdown,
            'peak_equity': self.peak_equity
        })
    
    def run_backtest(self, df: pd.DataFrame, signal_column: str = 'signal') -> Dict:
        """
        Run enhanced backtest on DataFrame
        """
        print(f"🚀 Running enhanced backtest on {len(df)} bars...")
        
        self.reset()
        
        if signal_column not in df.columns:
            print(f"❌ Signal column '{signal_column}' not found in DataFrame")
            return self.get_metrics()
        
        for i in range(len(df)):
            row = df.iloc[i]
            timestamp = df.index[i] if isinstance(df.index, pd.DatetimeIndex) else row.get('Timestamp')
            
            if pd.isna(row[signal_column]) or pd.isna(row['Close']):
                continue
            
            current_prices = {
                'open': row['Open'],
                'high': row['High'], 
                'low': row['Low'],
                'close': row['Close']
            }
            
            # Check stop loss / take profit for existing positions
            positions_to_close = []
            for position in self.positions:
                exit_reason = self.check_stop_loss_take_profit(position, row['Close'])
                if exit_reason:
                    positions_to_close.append((position, exit_reason))
            
            # Close positions that hit stop loss or take profit
            for position, reason in positions_to_close:
                self.close_position(position, timestamp, row['Close'], reason)
            
            # Handle new signals
            signal = row[signal_column]
            
            if signal == 1:  # Buy signal
                if len(self.positions) == 0 or (len(self.positions) > 0 and self.positions[0].side == "short"):
                    # Close short position if exists
                    if self.positions and self.positions[0].side == "short":
                        self.close_position(self.positions[0], timestamp, row['Close'], "signal")
                    # Open long position
                    self.open_position(timestamp, row['Close'], signal)
                    
            elif signal == -1:  # Sell signal
                if len(self.positions) > 0 and self.positions[0].side == "long":
                    # Close long position
                    self.close_position(self.positions[0], timestamp, row['Close'], "signal")
                    
                # Open short position if allowed
                if self.config.allow_short:
                    self.open_position(timestamp, row['Close'], signal)
            
            # Update equity curve
            self.update_equity_curve(timestamp, current_prices)
        
        # Close any remaining open positions at the end
        if self.positions:
            final_price = df.iloc[-1]['Close']
            final_timestamp = df.index[-1] if isinstance(df.index, pd.DatetimeIndex) else df.iloc[-1].get('Timestamp')
            for position in self.positions.copy():
                self.close_position(position, final_timestamp, final_price, "end_of_data")
        
        return self.get_metrics()
    
    def get_metrics(self) -> Dict:
        """Calculate comprehensive backtest metrics"""
        if not self.closed_trades:
            return {
                'total_trades': 0,
                'profitable_trades': 0,
                'success_rate': 0,
                'total_pnl': 0,
                'total_pnl_percentage': 0,
                'avg_trade_pnl': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'profit_factor': 0,
                'trades': []
            }
        
        # Basic metrics
        total_trades = len(self.closed_trades)
        profitable_trades = sum(1 for trade in self.closed_trades if trade.pnl > 0)
        success_rate = (profitable_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_pnl = sum(trade.pnl for trade in self.closed_trades)
        total_pnl_percentage = (total_pnl / self.config.initial_capital) * 100
        avg_trade_pnl = total_pnl / total_trades if total_trades > 0 else 0
        
        # Drawdown
        max_drawdown = max([dd['drawdown'] for dd in self.drawdown_curve]) if self.drawdown_curve else 0
        
        # Profit factor
        gross_profit = sum(trade.pnl for trade in self.closed_trades if trade.pnl > 0)
        gross_loss = abs(sum(trade.pnl for trade in self.closed_trades if trade.pnl < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        if len(self.equity_curve) > 1:
            returns = []
            for i in range(1, len(self.equity_curve)):
                ret = (self.equity_curve[i]['equity'] - self.equity_curve[i-1]['equity']) / self.equity_curve[i-1]['equity']
                returns.append(ret)
            
            if returns and np.std(returns) > 0:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)  # Annualized
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        return {
            'total_trades': total_trades,
            'profitable_trades': profitable_trades,
            'success_rate': round(success_rate, 2),
            'total_pnl': round(total_pnl, 2),
            'total_pnl_percentage': round(total_pnl_percentage, 2),
            'avg_trade_pnl': round(avg_trade_pnl, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'profit_factor': round(profit_factor, 2),
            'gross_profit': round(gross_profit, 2),
            'gross_loss': round(gross_loss, 2),
            'final_capital': round(self.capital, 2),
            'trades': [
                {
                    'entry_time': trade.entry_time,
                    'exit_time': trade.exit_time,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'pnl': trade.pnl,
                    'pnl_percentage': trade.pnl_percentage,
                    'side': trade.side,
                    'exit_reason': trade.exit_reason
                }
                for trade in self.closed_trades
            ],
            'equity_curve': self.equity_curve,
            'drawdown_curve': self.drawdown_curve
        }

# Backward compatibility function
def run_backtest(df: pd.DataFrame, initial_capital: float = 10000.0, 
                commission_per_trade: float = 0.001) -> Dict:
    """Backward compatibility function"""
    config = BacktestConfig(
        initial_capital=initial_capital,
        commission_rate=commission_per_trade
    )
    backtester = EnhancedBacktester(config)
    return backtester.run_backtest(df)

if __name__ == '__main__':
    # Test the enhanced backtester
    print("=== Testing Enhanced Backtester ===")
    
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    prices = [100]
    for _ in range(99):
        change = np.random.normal(0, 0.02)
        prices.append(prices[-1] * (1 + change))
    
    df = pd.DataFrame({
        'Timestamp': dates,
        'Open': prices,
        'High': [p * 1.02 for p in prices],
        'Low': [p * 0.98 for p in prices],
        'Close': prices,
        'Volume': np.random.randint(1000, 5000, 100)
    })
    df.set_index('Timestamp', inplace=True)
    
    # Generate simple signals
    df['signal'] = 0
    df.loc[df.index[::10], 'signal'] = 1  # Buy every 10 days
    df.loc[df.index[5::10], 'signal'] = -1  # Sell 5 days later
    
    # Test basic backtest
    config = BacktestConfig(
        initial_capital=10000,
        commission_rate=0.001,
        stop_loss_percentage=0.05,
        take_profit_percentage=0.10
    )
    
    backtester = EnhancedBacktester(config)
    results = backtester.run_backtest(df)
    
    print(f"📊 Backtest Results:")
    print(f"Total Trades: {results['total_trades']}")
    print(f"Success Rate: {results['success_rate']}%")
    print(f"Total P&L: ${results['total_pnl']:.2f} ({results['total_pnl_percentage']:.2f}%)")
    print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']}")
    print(f"Profit Factor: {results['profit_factor']}")
