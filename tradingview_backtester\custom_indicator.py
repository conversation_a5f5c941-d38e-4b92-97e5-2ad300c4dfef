import pandas as pd
import numpy as np

# (Keep PANDAS_TA_AVAILABLE and calculate_ma function as they are)
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
    print("pandas_ta library found and will be used for standard indicators.")
except ImportError:
    PANDAS_TA_AVAILABLE = False
    print("pandas_ta library not found. Manual implementations will be used.")

def calculate_ma(series: pd.Series, length: int, ma_type: str, volume_series: pd.Series = None):
    length = int(length)
    ma_type = ma_type.lower()
    if length <= 0 or len(series) < length:
        return pd.Series(np.nan, index=series.index)

    series_name = series.name if series.name else "unknown_series"

    if PANDAS_TA_AVAILABLE:
        if ma_type == 'sma': return series.ta.sma(length=length)
        elif ma_type == 'ema': return series.ta.ema(length=length)
        elif ma_type == 'wma': return series.ta.wma(length=length)
        elif ma_type == 'rma': return series.ta.rma(length=length)
        elif ma_type == 'vwma':
            if volume_series is not None and not volume_series.empty:
                return series.ta.vwma(length=length, volume=volume_series)
            else:
                print(f"VWMA requires volume for {series_name}, but not provided/empty. Defaulting to EMA.")
                return series.ta.ema(length=length)
        else:
            print(f"Unsupported MA type '{ma_type}' with pandas_ta for {series_name}. Defaulting to EMA.")
            return series.ta.ema(length=length)
    else: # Manual Fallbacks
        if ma_type == 'sma': return series.rolling(window=length, min_periods=length).mean()
        elif ma_type == 'ema': return series.ewm(span=length, adjust=False, min_periods=length).mean()
        elif ma_type == 'wma':
            weights = np.arange(1, length + 1)
            return series.rolling(window=length, min_periods=length).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
        elif ma_type == 'rma': return series.ewm(alpha=1/length, adjust=False, min_periods=length).mean()
        elif ma_type == 'vwma':
            if volume_series is not None and not volume_series.empty:
                aligned_volume = volume_series.reindex(series.index)
                vol_sum = aligned_volume.rolling(window=length).sum().replace(0, np.nan)
                return (series * aligned_volume).rolling(window=length).sum() / vol_sum
            else:
                print(f"VWMA requires volume for manual calc of {series_name}, not provided/empty. Defaulting to EMA.")
                return series.ewm(span=length, adjust=False, min_periods=length).mean()
        else:
            print(f"Unsupported MA type '{ma_type}' for manual calc of {series_name}. Defaulting to EMA.")
            return series.ewm(span=length, adjust=False, min_periods=length).mean()

def calculate_custom_indicator(df: pd.DataFrame, **params):
    # ... (previous content of the function up to Perfect Trail calculations) ...
    print(f"Custom indicator (Signal Gen Phase) called with {len(df)} bars and params: {params}")

    if not isinstance(df.index, pd.DatetimeIndex):
        if 'Timestamp' in df.columns:
            df_indexed = df.set_index('Timestamp')
        else:
            print("Error in custom_indicator: DataFrame must have a 'Timestamp' column or be a DatetimeIndex.")
            return df.copy(), {}
    else:
        df_indexed = df.copy()

    required_cols = ['Open', 'High', 'Low', 'Close']
    if not all(col in df_indexed.columns for col in required_cols):
        print(f"Error: DataFrame must contain Open, High, Low, Close. Got: {df_indexed.columns}")
        return df_indexed, {}
    if 'Volume' not in df_indexed.columns:
        print("Warning: 'Volume' column not found. Creating a dummy Volume column with zeros.")
        df_indexed['Volume'] = 0.0


    nwe_src_col = params.get("nwe_src_column", "Close")
    nwe_h = params.get("nwe_h", 8.0)
    nwe_mult = params.get("nwe_mult", 3.0)
    window_size_nwe = int(params.get("nwe_window_size", 250))

    pt_ma_type = params.get("pt_ma_type", "ema")
    pt_ma_length = int(params.get("pt_ma_length", 70))
    pt_atr_length = int(params.get("pt_atr_length", 200))
    pt_bands_dist = params.get("pt_bands_dist", 3.0)
    pt_band2_mult = params.get("pt_band2_mult", 1.5)
    pt_band3_mult = params.get("pt_band3_mult", 3.0)
    pt_confirm_bars = int(params.get("pt_confirm_bars", 1))

    macd_fast = int(params.get("macd_fast_length", 12))
    macd_slow = int(params.get("macd_slow_length", 26))
    macd_signal = int(params.get("macd_signal_length", 9))

    rsi_length = int(params.get("rsi_length", 14))
    dmi_length = int(params.get("dmi_length", 14))
    use_dmi_param = params.get("useDmi", True)

    signals = {}
    nwe_src = df_indexed[nwe_src_col]

    def gauss_pine(x, h_param):
        return np.exp(-(x**2) / (2 * h_param**2))

    coefs_arr = np.array([gauss_pine(i, nwe_h) for i in range(window_size_nwe + 1)])
    den = np.sum(coefs_arr)

    if den != 0:
        df_indexed['nwe_out'] = nwe_src.rolling(window=len(coefs_arr), min_periods=len(coefs_arr))                                    .apply(lambda x: np.sum(x * coefs_arr) / den, raw=True)
    else:
        df_indexed['nwe_out'] = np.nan

    df_indexed['nwe_mae'] = (nwe_src - df_indexed['nwe_out']).abs().rolling(window=window_size_nwe, min_periods=window_size_nwe).mean() * nwe_mult
    df_indexed['nwe_upper'] = df_indexed['nwe_out'] + df_indexed['nwe_mae']
    df_indexed['nwe_lower'] = df_indexed['nwe_out'] - df_indexed['nwe_mae']

    # --- Perfect Trail ---
    if PANDAS_TA_AVAILABLE:
        pt_atr = df_indexed.ta.atr(length=pt_atr_length)
    else:
        high_low = df_indexed['High'] - df_indexed['Low']
        high_prev_close = (df_indexed['High'] - df_indexed['Close'].shift(1)).abs()
        low_prev_close = (df_indexed['Low'] - df_indexed['Close'].shift(1)).abs()
        tr_series = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1, skipna=False)
        pt_atr = tr_series.ewm(alpha=1/pt_atr_length, adjust=False, min_periods=pt_atr_length).mean()

    df_indexed['pt_atr'] = pt_atr if pt_atr is not None else np.nan
    df_indexed['pt_ma'] = calculate_ma(df_indexed['Close'], pt_ma_length, pt_ma_type, volume_series=df_indexed['Volume'])

    df_indexed['pt_upper_band_calc'] = df_indexed['pt_ma'] + df_indexed['pt_atr'] * pt_bands_dist
    df_indexed['pt_lower_band_calc'] = df_indexed['pt_ma'] - df_indexed['pt_atr'] * pt_bands_dist

    trend_state = False
    trend_strength_state = 0.0

    close_series_np = df_indexed['Close'].to_numpy()
    upper_band_calc_np = df_indexed['pt_upper_band_calc'].to_numpy()
    lower_band_calc_np = df_indexed['pt_lower_band_calc'].to_numpy()
    pt_ma_np = df_indexed['pt_ma'].to_numpy()

    pt_trend_np = np.full(len(df_indexed), False, dtype=bool)
    pt_trend_strength_np = np.full(len(df_indexed), 0.0, dtype=float)
    pt_signal_np = np.full(len(df_indexed), False, dtype=bool) # For trail crossover signals
    pt_signal_type_list = ["none"] * len(df_indexed) # For trail crossover signals

    for i in range(len(df_indexed)):
        if pd.isna(upper_band_calc_np[i]) or pd.isna(lower_band_calc_np[i]) or pd.isna(pt_ma_np[i]):
            pt_trend_np[i] = trend_state
            pt_trend_strength_np[i] = trend_strength_state
            continue

        close_i = close_series_np[i]
        upper_calc_i = upper_band_calc_np[i]
        lower_calc_i = lower_band_calc_np[i]
        ma_i = pt_ma_np[i]
        ma_prev_i = pt_ma_np[i-1] if i > 0 and not pd.isna(pt_ma_np[i-1]) else ma_i

        current_bar_trend_strength = trend_strength_state
        if close_i > upper_calc_i: current_bar_trend_strength += 1
        elif close_i < lower_calc_i: current_bar_trend_strength -= 1
        trend_strength_state = current_bar_trend_strength * 0.9

        trend_confirmation_i = np.sign(current_bar_trend_strength)
        ma_slope_i = ma_i - ma_prev_i

        bullish_slope_confirm = ma_slope_i > 0
        bearish_slope_confirm = ma_slope_i < 0

        prev_trend_state = trend_state
        if trend_confirmation_i >= pt_confirm_bars and bullish_slope_confirm:
            trend_state = True
        elif trend_confirmation_i <= -pt_confirm_bars and bearish_slope_confirm:
            trend_state = False

        pt_trend_np[i] = trend_state
        pt_trend_strength_np[i] = current_bar_trend_strength

        if trend_state != prev_trend_state and i > 0 :
            pt_signal_np[i] = True # This is for the Perfect Trail's own crossover signal
            pt_signal_type_list[i] = "bullish" if trend_state else "bearish"

    df_indexed['pt_trend'] = pt_trend_np # This is the main trend (green/red)
    df_indexed['pt_trend_strength'] = pt_trend_strength_np
    df_indexed['pt_crossover_signal_present'] = pt_signal_np # PT specific crossover
    df_indexed['pt_crossover_signal_type'] = pt_signal_type_list # PT specific crossover type

    df_indexed['pt_band1'] = np.where(df_indexed['pt_trend'], df_indexed['pt_lower_band_calc'], df_indexed['pt_upper_band_calc'])
    strength_adj_pt = (df_indexed['pt_trend_strength'].abs() / 5.0).clip(0, 1) * 0.3
    band2_mult_adj_pt = pt_band2_mult * (1 + strength_adj_pt)
    band3_mult_adj_pt = pt_band3_mult * (1 + strength_adj_pt)

    df_indexed['pt_band2'] = np.where(df_indexed['pt_trend'],
                                     df_indexed['pt_lower_band_calc'] + df_indexed['pt_atr'] * band2_mult_adj_pt,
                                     df_indexed['pt_upper_band_calc'] - df_indexed['pt_atr'] * band2_mult_adj_pt)
    df_indexed['pt_band3'] = np.where(df_indexed['pt_trend'],
                                     df_indexed['pt_lower_band_calc'] + df_indexed['pt_atr'] * band3_mult_adj_pt,
                                     df_indexed['pt_upper_band_calc'] - df_indexed['pt_atr'] * band3_mult_adj_pt)

    # --- Bar Color and Signal Generation (based on pt_trend) ---
    # df_indexed['bar_color_code'] = np.where(df_indexed['pt_trend'], 'green', 'red') # Simple text color
    # df_indexed['signal'] = np.where(df_indexed['pt_trend'], 1, -1) # Simple signal

    conditions = [df_indexed['pt_trend'] == True, df_indexed['pt_trend'] == False]
    choices_signal = [1, -1] # 1 for Bullish, -1 for Bearish
    choices_color = ['rgba(0,180,0,0.7)', 'rgba(180,0,0,0.7)'] # Actual colors for plotly

    df_indexed['signal'] = np.select(conditions, choices_signal, default=0)
    df_indexed['bar_plot_color'] = np.select(conditions, choices_color, default='rgba(128,128,128,0.5)') # Default grey for no trend


    signals['perfect_trail_trend_direction'] = "bullish" if trend_state else "bearish"
    signals['perfect_trail_last_signal'] = df_indexed['pt_crossover_signal_type'].iloc[-1] if not df_indexed.empty and len(df_indexed['pt_crossover_signal_type']) > 0 else "none"

    # --- Standard Indicators ---
    if PANDAS_TA_AVAILABLE:
        print("Using pandas_ta for MACD, RSI, DMI/ADX.")
        df_indexed.ta.macd(fast=macd_fast, slow=macd_slow, signal=macd_signal, append=True, col_names=(f'macd_line', f'macd_hist', f'macd_signal_line'))
        df_indexed.ta.rsi(length=rsi_length, append=True, col_names=(f'rsi',))
        if use_dmi_param:
            dmi_df = df_indexed.ta.dmi(length=dmi_length, append=False)
            if dmi_df is not None:
                df_indexed[f'adx'] = dmi_df[f'ADX_{dmi_length}'] if f'ADX_{dmi_length}' in dmi_df else np.nan
                df_indexed[f'dmi_plus'] = dmi_df[f'DMP_{dmi_length}'] if f'DMP_{dmi_length}' in dmi_df else np.nan
                df_indexed[f'dmi_minus'] = dmi_df[f'DMN_{dmi_length}'] if f'DMN_{dmi_length}' in dmi_df else np.nan
            else: df_indexed[['adx', 'dmi_plus', 'dmi_minus']] = np.nan
        else: df_indexed[['adx', 'dmi_plus', 'dmi_minus']] = np.nan
    else: # Manual Fallbacks
        print("Manual MACD, RSI, DMI/ADX calculation.")
        ema_fast = calculate_ma(df_indexed['Close'], macd_fast, 'ema', volume_series=df_indexed['Volume'])
        ema_slow = calculate_ma(df_indexed['Close'], macd_slow, 'ema', volume_series=df_indexed['Volume'])
        df_indexed['macd_line'] = ema_fast - ema_slow
        df_indexed['macd_signal_line'] = calculate_ma(df_indexed['macd_line'], macd_signal, 'ema', volume_series=df_indexed['Volume']) # MACD signal line is EMA of MACD line
        df_indexed['macd_hist'] = df_indexed['macd_line'] - df_indexed['macd_signal_line']

        delta = df_indexed['Close'].diff()
        gain = delta.where(delta > 0, 0).fillna(0)
        loss = (-delta).where(delta < 0, 0).fillna(0)
        avg_gain = gain.ewm(com=rsi_length - 1, adjust=False, min_periods=rsi_length).mean()
        avg_loss = loss.ewm(com=rsi_length - 1, adjust=False, min_periods=rsi_length).mean()
        rs = avg_gain / avg_loss.replace(0, np.nan)
        df_indexed['rsi'] = (100 - (100 / (1 + rs))).bfill()

        if use_dmi_param:
            tr1 = df_indexed['High'] - df_indexed['Low']
            tr2 = (df_indexed['High'] - df_indexed['Close'].shift(1)).abs()
            tr3 = (df_indexed['Low'] - df_indexed['Close'].shift(1)).abs()
            tr_series = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1, skipna=False)
            str_wilder = tr_series.ewm(com=dmi_length - 1, adjust=False, min_periods=dmi_length).mean()

            up_move = df_indexed['High'] - df_indexed['High'].shift(1)
            down_move = df_indexed['Low'].shift(1) - df_indexed['Low']
            pdm = up_move.where((up_move > down_move) & (up_move > 0), 0)
            ndm = down_move.where((down_move > up_move) & (down_move > 0), 0)
            spdm = pdm.ewm(com=dmi_length - 1, adjust=False, min_periods=dmi_length).mean()
            sndm = ndm.ewm(com=dmi_length - 1, adjust=False, min_periods=dmi_length).mean()

            str_wilder_safe = str_wilder.replace(0, np.nan)
            df_indexed['dmi_plus'] = (100 * (spdm / str_wilder_safe)).bfill()
            df_indexed['dmi_minus'] = (100 * (sndm / str_wilder_safe)).bfill()

            dx_denominator = (spdm + sndm).replace(0, np.nan)
            dx_series = (100 * (abs(spdm - sndm) / dx_denominator)).bfill() # bfill dx before ADX calculation
            df_indexed['adx'] = dx_series.ewm(com=dmi_length - 1, adjust=False, min_periods=dmi_length).mean().bfill()
        else:
            df_indexed[['adx', 'dmi_plus', 'dmi_minus']] = np.nan

    print("Custom indicator (Signal Gen Phase) calculations complete.")
    return df_indexed, signals

# ... (keep if __name__ == '__main__' as is, but ensure 'bar_plot_color' and 'signal' are printed for verification)
if __name__ == '__main__':
    sample_data_dict = {
       'Timestamp': pd.date_range(start='2023-01-01', periods=300, freq='D'),
       'Open': np.random.rand(300) * 50 + 16000,
       'High': np.random.rand(300) * 50 + 16050,
       'Low': np.random.rand(300) * 50 + 15950,
       'Close': np.random.rand(300) * 50 + 16000,
       'Volume': np.random.rand(300) * 1000 + 50000
    }
    test_df = pd.DataFrame(sample_data_dict)
    test_df['High'] = np.maximum(test_df['High'], test_df[['Open', 'Close']].max(axis=1) + np.random.uniform(0,5,300))
    test_df['Low'] = np.minimum(test_df['Low'], test_df[['Open', 'Close']].min(axis=1) - np.random.uniform(0,5,300))
    test_df = test_df.set_index('Timestamp')

    indicator_params = {
       "nwe_src_column": "Close", "nwe_h": 8.0, "nwe_mult": 3.0, "nwe_window_size": 50,
       "pt_ma_type": "ema", "pt_ma_length": 20, "pt_atr_length": 14, "pt_bands_dist": 2.0,
       "pt_band2_mult": 1.5, "pt_band3_mult": 3.0, "pt_confirm_bars": 1,
       "macd_fast_length": 12, "macd_slow_length": 26, "macd_signal_length": 9,
       "rsi_length": 14, "dmi_length": 14, "useDmi": True
    }

    output_df, output_signals = calculate_custom_indicator(test_df.copy(), **indicator_params)
    print("\nOutput DataFrame from custom_indicator (tail with signals and colors):")
    print(output_df[['Close', 'pt_trend', 'bar_plot_color', 'signal', 'nwe_upper', 'pt_band1', 'macd_line', 'rsi', 'adx']].tail())
    print("\nOutput Signals from custom_indicator:")
    print(output_signals)
