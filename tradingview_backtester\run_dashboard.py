#!/usr/bin/env python3
"""
Launch script for the Enhanced Trading Dashboard
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'numpy',
        'plotly',
        'requests',
        'yfinance'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """Setup environment variables and paths"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent.absolute()
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set environment variables for Streamlit
    os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
    os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
    os.environ['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'

def run_dashboard(port=8501, host='localhost', debug=False):
    """Run the Streamlit dashboard"""
    
    print("🚀 Starting Enhanced Trading Dashboard...")
    print(f"📍 URL: http://{host}:{port}")
    print("🔄 Loading application...")
    
    # Streamlit command
    cmd = [
        sys.executable, '-m', 'streamlit', 'run',
        'dashboard.py',
        '--server.port', str(port),
        '--server.address', host,
        '--server.headless', 'true',
        '--server.fileWatcherType', 'none'
    ]
    
    if debug:
        cmd.extend(['--logger.level', 'debug'])
    
    try:
        # Run Streamlit
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running dashboard: {e}")
        return False
    except FileNotFoundError:
        print("❌ Streamlit not found. Please install it using: pip install streamlit")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Enhanced Trading Dashboard Launcher')
    parser.add_argument('--port', type=int, default=8501, help='Port to run the dashboard on')
    parser.add_argument('--host', default='localhost', help='Host to run the dashboard on')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--check-deps', action='store_true', help='Check dependencies only')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("📈 ENHANCED TRADING DASHBOARD LAUNCHER")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_deps:
        print("✅ All dependencies are installed")
        return
    
    # Setup environment
    setup_environment()
    
    # Run dashboard
    success = run_dashboard(args.port, args.host, args.debug)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
